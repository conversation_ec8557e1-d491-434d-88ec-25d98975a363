/**
 * 测试最终修复后的bitmap.elements处理
 * 验证是否与后端逻辑一致
 */

import { PropertyGenerator } from '../property/PropertyGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 使用您提供的真实数据
function createFinalTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  const elementsValue = [
    {
      "type": "text",
      "text": "Project5",
      "x": 17,
      "y": 104,
      "maxWidth": 776,
      "lineHeight": 48,
      "align": "center",
      "bounds": {
        "x": 20,
        "y": 156,
        "width": 776,
        "height": 48
      },
      "textColor": "#c56666",
      "fontSize": 71,
      "outlineWidth": 8.5
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/enemies/Actor1_6.png",
        "width": 178,
        "height": 302
      },
      "sx": 0,
      "sy": 0,
      "sw": 178,
      "sh": 302,
      "dx": 555,
      "dy": 8,
      "dw": 178,
      "dh": 302,
      "bounds": {
        "x": 319,
        "y": 161,
        "width": 178,
        "height": 302
      }
    }
  ];
  
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: Date.now()
  });

  return {
    id: 'final_bitmap_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
}

export function testFinalBitmapFix(): void {
  console.log('🔧 测试最终修复后的bitmap.elements处理...');
  
  const generator = new PropertyGenerator();
  const testOperation = createFinalTestOperation();
  
  try {
    const result = generator.generate(testOperation);
    
    console.log('生成的代码:');
    console.log('='.repeat(80));
    console.log(result.code);
    console.log('='.repeat(80));
    
    // 验证代码内容
    const code = result.code;
    const lines = code.split('\n');
    
    // 关键验证点
    const hasElementsProcessing = code.includes('_bitmap.elements');
    const hasImageLoading = code.includes('ImageManager.loadBitmapFromUrl');
    const hasAsyncCallback = code.includes('addLoadListener');
    
    // 最重要的验证：是否正确分离了source
    const hasDirectSourceAssignment = code.includes('"source":{"_url"');
    const hasProperSeparation = !hasDirectSourceAssignment;
    
    // 验证是否有正确的异步加载结构
    const hasImagePathVariable = code.includes('imagePath_1');
    const hasImageBitmapVariable = code.includes('imageBitmap_1');
    const hasSourceSetting = code.includes('.source._url') && code.includes('.source.bitmap');
    
    // 验证变量名是否正确
    const hasCorrectVariableName = code.includes('target_sprite_Scene_Title_2');
    const hasIncorrectVariableName = code.includes('target_sprite_.');
    
    console.log('\n🔍 详细验证结果:');
    console.log('- 包含elements处理:', hasElementsProcessing ? '✅' : '❌');
    console.log('- 包含图片加载:', hasImageLoading ? '✅' : '❌');
    console.log('- 异步加载回调:', hasAsyncCallback ? '✅' : '❌');
    console.log('- 正确分离source:', hasProperSeparation ? '✅' : '❌');
    console.log('- 图片路径变量:', hasImagePathVariable ? '✅' : '❌');
    console.log('- 图片bitmap变量:', hasImageBitmapVariable ? '✅' : '❌');
    console.log('- 设置source属性:', hasSourceSetting ? '✅' : '❌');
    console.log('- 正确的变量名:', hasCorrectVariableName ? '✅' : '❌');
    console.log('- 没有错误变量名:', !hasIncorrectVariableName ? '✅' : '❌');
    
    // 检查代码结构
    const hasProperStructure = lines.some(line => 
      line.includes('设置包含图片的 elements 数组')
    );
    const hasImageLoadingLoop = lines.some(line => 
      line.includes('为元素 1 加载图片')
    );
    const hasCleanElementsArray = lines.some(line => 
      line.includes('_bitmap.elements = [') && !line.includes('"source"')
    );
    
    console.log('- 正确的代码结构:', hasProperStructure ? '✅' : '❌');
    console.log('- 图片加载循环:', hasImageLoadingLoop ? '✅' : '❌');
    console.log('- 清理的elements数组:', hasCleanElementsArray ? '✅' : '❌');
    
    // 总体评估
    const criticalTestsPassed = hasElementsProcessing && 
                               hasImageLoading && 
                               hasProperSeparation && 
                               hasAsyncCallback &&
                               hasCorrectVariableName &&
                               !hasIncorrectVariableName;
    
    const allTestsPassed = criticalTestsPassed &&
                          hasImagePathVariable &&
                          hasImageBitmapVariable &&
                          hasSourceSetting &&
                          hasProperStructure &&
                          hasImageLoadingLoop &&
                          hasCleanElementsArray;
    
    console.log('\n🎯 关键测试结果:', criticalTestsPassed ? '✅ 通过' : '❌ 失败');
    console.log('🎯 全部测试结果:', allTestsPassed ? '✅ 全部通过！' : '❌ 部分失败');
    
    // 详细问题分析
    if (!criticalTestsPassed) {
      console.log('\n❌ 关键问题:');
      if (!hasElementsProcessing) console.log('- 没有处理elements属性');
      if (!hasImageLoading) console.log('- 没有图片加载逻辑');
      if (!hasProperSeparation) console.log('- 直接设置了包含source的数组');
      if (!hasAsyncCallback) console.log('- 没有异步加载回调');
      if (!hasCorrectVariableName) console.log('- 变量名不正确');
      if (hasIncorrectVariableName) console.log('- 存在错误的变量名');
      
      if (hasDirectSourceAssignment) {
        console.log('\n包含source的问题行:');
        lines.forEach((line, index) => {
          if (line.includes('"source":{"_url"')) {
            console.log(`第${index + 1}行: ${line.trim()}`);
          }
        });
      }
      
      if (hasIncorrectVariableName) {
        console.log('\n错误变量名的问题行:');
        lines.forEach((line, index) => {
          if (line.includes('target_sprite_.')) {
            console.log(`第${index + 1}行: ${line.trim()}`);
          }
        });
      }
    }
    
    // 与期望的后端格式对比
    console.log('\n📋 与后端格式对比:');
    console.log('期望的代码结构:');
    console.log('1. 设置不包含source的elements数组');
    console.log('2. 为每个图片元素生成异步加载代码');
    console.log('3. 在加载完成后设置source属性');
    console.log('4. 使用正确的变量名');
    
    if (allTestsPassed) {
      console.log('\n🎉 恭喜！代码生成已完全符合后端逻辑！');
    } else if (criticalTestsPassed) {
      console.log('\n⚠️ 基本功能正常，但还有一些细节需要完善。');
    } else {
      console.log('\n💥 还有关键问题需要解决。');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testFinalBitmapFix();
}
