/**
 * 调试BitmapGenerator的图片检测逻辑
 */

import { BitmapGenerator } from '../property/BitmapGenerator';
import { BitmapElementsService } from '../../services/bitmap/BitmapElementsService';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 使用您提供的真实数据
function createRealElementsData() {
  return [
    {
      "type": "text",
      "text": "Project5",
      "x": 17,
      "y": 104,
      "maxWidth": 776,
      "lineHeight": 48,
      "align": "center",
      "bounds": {
        "x": 20,
        "y": 156,
        "width": 776,
        "height": 48
      },
      "textColor": "#c56666",
      "fontSize": 71,
      "outlineWidth": 8.5
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/enemies/Actor1_6.png",
        "width": 178,
        "height": 302
      },
      "sx": 0,
      "sy": 0,
      "sw": 178,
      "sh": 302,
      "dx": 555,
      "dy": 8,
      "dw": 178,
      "dh": 302,
      "bounds": {
        "x": 319,
        "y": 161,
        "width": 178,
        "height": 302
      }
    }
  ];
}

export function debugBitmapGenerator(): void {
  console.log('🔍 调试BitmapGenerator图片检测逻辑...');
  
  const elementsData = createRealElementsData();
  console.log('测试数据:', JSON.stringify(elementsData, null, 2));
  
  // 测试BitmapElementsService的检测
  const hasImages1 = BitmapElementsService.hasImageElements(elementsData);
  console.log('BitmapElementsService.hasImageElements:', hasImages1);
  
  // 测试分离功能
  const separated = BitmapElementsService.separateElements(elementsData);
  console.log('分离结果:');
  console.log('- 图片URL数量:', separated.imageUrls.length);
  console.log('- 图片URLs:', separated.imageUrls);
  console.log('- 清理后的元素:', separated.cleanElements);
  
  // 创建BitmapGenerator实例并测试
  const generator = new BitmapGenerator();
  
  // 创建测试操作
  const properties = new Map<string, PropertyChange>();
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsData,
    propertyType: 'array',
    timestamp: Date.now()
  });
  
  const testOperation: PropertyModificationInfo = {
    id: 'debug_bitmap_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsData,
    propertyType: 'array'
  };
  
  console.log('\n生成代码...');
  try {
    const result = generator.generate(testOperation, {});
    
    console.log('生成的代码:');
    console.log('='.repeat(80));
    console.log(result.code);
    console.log('='.repeat(80));
    
    // 分析生成的代码
    const code = result.code;
    const lines = code.split('\n');
    
    console.log('\n代码分析:');
    console.log('- 总行数:', lines.length);
    console.log('- 包含"检查数组中是否包含图片元素":', code.includes('检查数组中是否包含图片元素'));
    console.log('- 包含"设置不包含图片source的elements数组":', code.includes('设置不包含图片source的elements数组'));
    console.log('- 包含"ImageManager.loadBitmapFromUrl":', code.includes('ImageManager.loadBitmapFromUrl'));
    console.log('- 包含"为元素 1 加载图片":', code.includes('为元素 1 加载图片'));
    console.log('- 包含"source._url":', code.includes('source._url'));
    console.log('- 包含"addLoadListener":', code.includes('addLoadListener'));
    
    // 检查是否直接设置了包含source的数组
    const hasDirectSourceAssignment = code.includes('"source":{"_url"');
    console.log('- 直接设置包含source的数组:', hasDirectSourceAssignment ? '❌ 是（错误）' : '✅ 否（正确）');
    
    if (hasDirectSourceAssignment) {
      console.log('\n❌ 问题：代码直接设置了包含source的elements数组！');
      console.log('这意味着图片检测逻辑没有正常工作。');
      
      // 查找包含source的行
      lines.forEach((line, index) => {
        if (line.includes('"source":{"_url"')) {
          console.log(`第${index + 1}行: ${line.trim()}`);
        }
      });
    } else {
      console.log('\n✅ 正确：代码使用了异步图片加载逻辑！');
    }
    
  } catch (error) {
    console.error('❌ 生成代码时出错:', error);
  }
}

// 如果直接运行此文件，执行调试
if (typeof window === 'undefined') {
  debugBitmapGenerator();
}
