/**
 * 统一的Bitmap Elements处理服务
 * 整合SpriteEditorDialog和BitmapGenerator的bitmap.elements处理逻辑
 */

import { recordPropertyModification } from '../backendServer/BackendService';

export interface ElementChange {
  type: 'added' | 'removed' | 'modified';
  elementType: 'text' | 'image';
  index: number;
  element?: any;
  oldElement?: any;
  newElement?: any;
  changedProperties?: string[];
}

export interface CleanElement {
  type: 'text' | 'image';
  [key: string]: any;
}

/**
 * 统一的Bitmap Elements处理服务
 */
export class BitmapElementsService {
  
  /**
   * 清理elements数组中的循环引用，使其可以安全地序列化为JSON
   */
  static cleanElementsForSerialization(elements: any[]): CleanElement[] {
    return elements.map(element => {
      const cleanElement = { ...element };

      if (element.type === 'image' && element.source) {
        // 对于图像元素，只保留必要的source信息
        cleanElement.source = {
          _url: element.source._url,
          width: element.source.width,
          height: element.source.height,
          // 移除所有可能导致循环引用的复杂对象
        };
      }

      return cleanElement;
    });
  }

  /**
   * 深度比较两个elements数组，检测变化
   */
  static detectElementsChanges(originalElements: any[], newElements: any[]): ElementChange[] {
    const changes: ElementChange[] = [];

    // 创建元素映射，用于快速查找
    const originalMap = new Map();
    const newMap = new Map();

    // 为原始元素创建唯一标识
    originalElements.forEach((element, index) => {
      const key = element.type === 'text'
        ? `text_${element.x}_${element.y}_${element.text}`
        : `image_${element.dx}_${element.dy}_${element.dw}_${element.dh}`;
      originalMap.set(key, { element, index });
    });

    // 为新元素创建唯一标识
    newElements.forEach((element, index) => {
      const key = element.type === 'text'
        ? `text_${element.x}_${element.y}_${element.text}`
        : `image_${element.dx}_${element.dy}_${element.dw}_${element.dh}`;
      newMap.set(key, { element, index });
    });

    // 检测新增的元素
    newMap.forEach((newItem, key) => {
      if (!originalMap.has(key)) {
        changes.push({
          type: 'added',
          elementType: newItem.element.type,
          index: newItem.index,
          element: newItem.element
        });
      }
    });

    // 检测删除的元素
    originalMap.forEach((originalItem, key) => {
      if (!newMap.has(key)) {
        changes.push({
          type: 'removed',
          elementType: originalItem.element.type,
          index: originalItem.index,
          element: originalItem.element
        });
      }
    });

    // 检测修改的元素（基于位置匹配）
    originalElements.forEach((originalElement, originalIndex) => {
      const matchingNewElement = newElements.find((newElement) => {
        if (originalElement.type !== newElement.type) return false;

        if (originalElement.type === 'text') {
          // 文本元素：基于位置匹配
          return Math.abs(originalElement.x - newElement.x) < 5 &&
            Math.abs(originalElement.y - newElement.y) < 5;
        } else {
          // 图像元素：基于位置匹配
          return Math.abs(originalElement.dx - newElement.dx) < 5 &&
            Math.abs(originalElement.dy - newElement.dy) < 5;
        }
      });

      if (matchingNewElement) {
        const changedProperties = this.detectPropertyChanges(originalElement, matchingNewElement);

        if (changedProperties.length > 0) {
          changes.push({
            type: 'modified',
            elementType: originalElement.type,
            index: originalIndex,
            oldElement: originalElement,
            newElement: matchingNewElement,
            changedProperties
          });
        }
      }
    });

    return changes;
  }

  /**
   * 检测单个元素的属性变化
   */
  private static detectPropertyChanges(originalElement: any, newElement: any): string[] {
    const changedProperties: string[] = [];

    if (originalElement.type === 'text') {
      // 文本元素属性检测
      const textProperties = ['text', 'x', 'y', 'maxWidth', 'lineHeight', 'align', 'outlineColor'];
      textProperties.forEach(prop => {
        if (originalElement[prop] !== newElement[prop]) {
          changedProperties.push(prop);
        }
      });
    } else if (originalElement.type === 'image') {
      // 图像元素属性检测
      const imageProperties = ['dx', 'dy', 'dw', 'dh', 'sx', 'sy', 'sw', 'sh'];
      imageProperties.forEach(prop => {
        if (originalElement[prop] !== newElement[prop]) {
          changedProperties.push(prop);
        }
      });

      // 检测source变化
      if (originalElement.source && newElement.source) {
        if (originalElement.source._url !== newElement.source._url) {
          changedProperties.push('source._url');
        }
        if (originalElement.source.width !== newElement.source.width) {
          changedProperties.push('source.width');
        }
        if (originalElement.source.height !== newElement.source.height) {
          changedProperties.push('source.height');
        }
      } else if (originalElement.source !== newElement.source) {
        changedProperties.push('source');
      }
    }

    return changedProperties;
  }

  /**
   * 保存elements变化到后端
   */
  static async saveElementsChangesToBackend(sprite: any, changes: ElementChange[]): Promise<void> {
    if (!sprite || changes.length === 0) {
      console.log('没有变化需要保存');
      return;
    }

    console.log('检测到的变化:', changes);

    try {
      // 为每个变化创建后端记录
      for (const change of changes) {
        let fieldName = '';
        let newValue = null;

        switch (change.type) {
          case 'added':
          case 'removed':
            // 新增或删除元素：记录整个elements数组（清理后）
            fieldName = '_bitmap.elements';
            newValue = this.cleanElementsForSerialization(sprite.bitmap.elements);
            break;

          case 'modified':
            // 修改元素：根据变化的属性记录
            if (change.changedProperties && change.changedProperties.length > 0) {
              for (const property of change.changedProperties) {
                fieldName = `_bitmap.elements[${change.index}].${property}`;
                newValue = change.newElement![property];

                // 如果是source属性，需要清理
                if (property === 'source' && newValue && typeof newValue === 'object') {
                  newValue = {
                    _url: newValue._url,
                    width: newValue.width,
                    height: newValue.height,
                  };
                }

                // 记录单个属性修改
                try {
                  await recordPropertyModification(sprite, fieldName, newValue);
                  console.log(`已记录属性修改: ${fieldName} = ${JSON.stringify(newValue)}`);
                } catch (error) {
                  console.error(`记录属性修改失败:`, error);
                }
              }
              continue; // 跳过下面的通用记录
            }
            break;
        }

        // 记录通用变化（新增、删除、或没有具体属性的修改）
        if (fieldName) {
          try {
            await recordPropertyModification(sprite, fieldName, newValue);
            console.log(`已记录变化: ${change.type} - ${fieldName}`);
          } catch (error) {
            console.error(`记录变化失败:`, error);
          }
        }
      }

      console.log('所有变化已成功保存到后端');
    } catch (error) {
      console.error('保存变化到后端时出错:', error);
      throw error;
    }
  }

  /**
   * 检查elements数组中是否包含图片元素
   */
  static hasImageElements(elements: any[]): boolean {
    if (!Array.isArray(elements)) return false;
    return elements.some(element => element && element.type === 'image');
  }

  /**
   * 分离图片元素和文本元素
   */
  static separateElements(elements: any[]): {
    textElements: any[];
    imageElements: any[];
    imageUrls: Array<{ index: number; url: string; width?: number; height?: number }>;
    cleanElements: any[];
  } {
    const textElements: any[] = [];
    const imageElements: any[] = [];
    const imageUrls: Array<{ index: number; url: string; width?: number; height?: number }> = [];
    const cleanElements: any[] = [];

    elements.forEach((element, index) => {
      if (element && element.type === 'image') {
        imageElements.push(element);
        
        // 提取图片URL信息
        if (element.source && element.source._url) {
          imageUrls.push({
            index,
            url: element.source._url,
            width: element.source.width,
            height: element.source.height
          });
        }

        // 创建不包含source的元素副本
        const cleanElement = { ...element };
        delete cleanElement.source;
        cleanElements.push(cleanElement);
      } else if (element && element.type === 'text') {
        textElements.push(element);
        cleanElements.push(element);
      } else {
        cleanElements.push(element);
      }
    });

    return {
      textElements,
      imageElements,
      imageUrls,
      cleanElements
    };
  }

  /**
   * 验证elements数组的有效性
   */
  static validateElements(elements: any[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!Array.isArray(elements)) {
      errors.push('elements必须是数组');
      return { isValid: false, errors };
    }

    elements.forEach((element, index) => {
      if (!element || typeof element !== 'object') {
        errors.push(`元素 ${index} 不是有效对象`);
        return;
      }

      if (!element.type || !['text', 'image'].includes(element.type)) {
        errors.push(`元素 ${index} 缺少有效的type属性`);
      }

      if (element.type === 'text') {
        if (typeof element.text !== 'string') {
          errors.push(`文本元素 ${index} 缺少text属性`);
        }
        if (typeof element.x !== 'number' || typeof element.y !== 'number') {
          errors.push(`文本元素 ${index} 缺少有效的x,y坐标`);
        }
      } else if (element.type === 'image') {
        const requiredProps = ['dx', 'dy', 'dw', 'dh', 'sx', 'sy', 'sw', 'sh'];
        requiredProps.forEach(prop => {
          if (typeof element[prop] !== 'number') {
            errors.push(`图像元素 ${index} 缺少有效的${prop}属性`);
          }
        });
      }
    });

    return { isValid: errors.length === 0, errors };
  }
}
