/**
 * 测试完善后的BitmapGenerator和FilterGenerator
 * 验证bitmap.elements和滤镜处理是否完整
 */

import { BitmapGenerator } from '../property/BitmapGenerator';
import { FilterGenerator } from '../property/FilterGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建bitmap测试操作
function createBitmapTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  // 测试elements数组（包含图片和文本）
  const elementsValue = [
    {
      type: 'image',
      bounds: { x: 5, y: 3, width: 144, height: 129 },
      dh: 129,
      dw: 144,
      dx: 5,
      dy: 3,
      sh: 129,
      source: { _url: '../projects/Project4/img/faces/Actor1.png' },
      sw: 144,
      sx: 0,
      sy: 7
    },
    {
      type: 'text',
      align: 'left',
      bounds: { x: 184, y: 13, width: 168, height: 36 },
      lineHeight: 36,
      maxWidth: 168,
      text: '里德',
      x: 184,
      y: 13
    }
  ];
  
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: Date.now()
  });
  
  // 测试单个elements属性修改
  properties.set('_bitmap.elements[0].dx', {
    propertyName: '_bitmap.elements[0].dx',
    oldValue: 5,
    newValue: 10,
    propertyType: 'number',
    timestamp: Date.now()
  });

  return {
    id: 'bitmap_test_operation',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '0'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
}

// 创建滤镜测试操作
function createFilterTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  // 测试添加滤镜操作
  properties.set('filters_operation_add_blur', {
    propertyName: 'filters_operation_add_blur',
    oldValue: null,
    newValue: {
      operation: 'add',
      filterType: 'blur',
      params: { blur: 5.0, quality: 8 },
      insertIndex: 0
    },
    propertyType: 'object',
    timestamp: Date.now()
  });
  
  // 测试滤镜参数修改
  properties.set('filters[0].blur', {
    propertyName: 'filters[0].blur',
    oldValue: 2.0,
    newValue: 8.0,
    propertyType: 'number',
    timestamp: Date.now()
  });
  
  // 测试删除滤镜操作
  properties.set('filters_operation_remove', {
    propertyName: 'filters_operation_remove',
    oldValue: null,
    newValue: {
      operation: 'remove',
      filterIndex: 1
    },
    propertyType: 'object',
    timestamp: Date.now()
  });

  return {
    id: 'filter_test_operation',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '1'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: 'filters_operation_add_blur',
    oldValue: null,
    newValue: {
      operation: 'add',
      filterType: 'blur',
      params: { blur: 5.0, quality: 8 }
    },
    propertyType: 'object'
  };
}

// 测试BitmapGenerator
export function testBitmapGenerator(): void {
  console.log('开始测试BitmapGenerator...');
  
  const generator = new BitmapGenerator();
  const testOperation = createBitmapTestOperation();
  
  try {
    const result = generator.generate(testOperation, {});
    
    console.log('BitmapGenerator生成的代码:');
    console.log('='.repeat(60));
    console.log(result.code);
    console.log('='.repeat(60));
    
    // 验证代码内容
    const code = result.code;
    const hasElementsProcessing = code.includes('_bitmap.elements');
    const hasImageLoading = code.includes('ImageManager.loadBitmapFromUrl');
    const hasElementsArray = code.includes('elements数组');
    const hasImageElements = code.includes('图片元素');
    
    console.log('BitmapGenerator验证结果:');
    console.log('- 包含elements处理:', hasElementsProcessing ? '✅' : '❌');
    console.log('- 包含图片加载:', hasImageLoading ? '✅' : '❌');
    console.log('- 包含elements数组处理:', hasElementsArray ? '✅' : '❌');
    console.log('- 包含图片元素处理:', hasImageElements ? '✅' : '❌');
    
    const bitmapTestPassed = hasElementsProcessing && hasImageLoading && hasElementsArray;
    console.log('BitmapGenerator测试结果:', bitmapTestPassed ? '✅ 通过' : '❌ 失败');
    
  } catch (error) {
    console.error('BitmapGenerator测试失败:', error);
  }
}

// 测试FilterGenerator
export function testFilterGenerator(): void {
  console.log('\n开始测试FilterGenerator...');
  
  const generator = new FilterGenerator();
  const testOperation = createFilterTestOperation();
  
  try {
    const result = generator.generate(testOperation, {});
    
    console.log('FilterGenerator生成的代码:');
    console.log('='.repeat(60));
    console.log(result.code);
    console.log('='.repeat(60));
    
    // 验证代码内容
    const code = result.code;
    const hasFilterOperations = code.includes('滤镜操作');
    const hasBlurFilter = code.includes('BlurFilter');
    const hasFilterParams = code.includes('滤镜参数');
    const hasFilterArray = code.includes('.filters');
    const hasRemoveOperation = code.includes('删除滤镜');
    
    console.log('FilterGenerator验证结果:');
    console.log('- 包含滤镜操作:', hasFilterOperations ? '✅' : '❌');
    console.log('- 包含模糊滤镜:', hasBlurFilter ? '✅' : '❌');
    console.log('- 包含滤镜参数修改:', hasFilterParams ? '✅' : '❌');
    console.log('- 包含滤镜数组操作:', hasFilterArray ? '✅' : '❌');
    console.log('- 包含删除操作:', hasRemoveOperation ? '✅' : '❌');
    
    const filterTestPassed = hasFilterOperations && hasBlurFilter && hasFilterParams && hasFilterArray;
    console.log('FilterGenerator测试结果:', filterTestPassed ? '✅ 通过' : '❌ 失败');
    
  } catch (error) {
    console.error('FilterGenerator测试失败:', error);
  }
}

// 运行所有测试
export function runAllTests(): void {
  console.log('🧪 开始测试完善后的BitmapGenerator和FilterGenerator');
  console.log('='.repeat(80));
  
  testBitmapGenerator();
  testFilterGenerator();
  
  console.log('\n='.repeat(80));
  console.log('🎯 所有测试完成！');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  runAllTests();
}
