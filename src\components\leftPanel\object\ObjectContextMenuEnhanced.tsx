import React, { useState, useCallback, useEffect } from "react";
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Collapse,
  List,
} from "@mui/material";
import AddPhotoAlternateIcon from "@mui/icons-material/AddPhotoAlternate";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import FolderIcon from "@mui/icons-material/Folder";
import WindowIcon from "@mui/icons-material/Window";
import SmartButtonIcon from "@mui/icons-material/SmartButton";
import DeleteIcon from "@mui/icons-material/Delete";
import GridViewIcon from "@mui/icons-material/GridView";
import ClassIcon from "@mui/icons-material/Class";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { TreeNode, NodeType } from "../../../utils/tree/TreeNodeTypes";
import { isContainer as checkIsContainer } from "../../../utils/tree/TreeUtils";
import useProjectStore from "../../../store/Store";
import {
  getMergedTypeTemplates,
  createObjectByType,
  TypeTemplate,
} from "../../../types/components/TreeTypes";
import {
  recordObjectCreation,
  recordObjectDeletion,
} from "../../../services/backendServer/objectLifecycle";
import EditIcon from "@mui/icons-material/Edit";
// 获取全局PIXI对象
const PIXI = (window as any).PIXI;

export interface ObjectContextMenuEnhancedProps {
  open: boolean;
  anchorPosition: { top: number; left: number } | null;
  nodeId?: string | null; // 可选，不再使用
  node?: TreeNode; // 添加节点对象，用于创建子节点
  onClose: () => void;
  updateTree?: () => void; // 添加更新树的方法
  customTypes?: string[]; // 自定义类型列表，保留向后兼容性
}

const ObjectContextMenuEnhanced: React.FC<ObjectContextMenuEnhancedProps> = ({
  open,
  anchorPosition,
  node,
  onClose,
  updateTree,
}) => {
  // 状态
  const [customTypesOpen, setCustomTypesOpen] = useState<boolean>(false);
  const [typeTemplates, setTypeTemplates] = useState<TypeTemplate[]>([]);
  // const setTypeEditMode = useProjectStore((state) => state.setTypeEditMode);
  // 获取全局状态
  const selectedObjectsState = useProjectStore(
    (state) => state.selectedObjects
  );
  const setSelectedObjects = useProjectStore(
    (state) => state.setSelectedObjects
  );
  // 处理进入类型编辑
  const handleEnterTypeEdit = () => {
    if (node && node.object) {
      // 先选中当前对象
      setSelectedObjects([node.object], "object");
      // 然后进入类型编辑模式
      // setTypeEditMode(true);
      console.log("进入类型编辑模式，对象:", node.object);
    }
    onClose();
  };
  // 当菜单打开时加载类型模板
  useEffect(() => {
    if (open) {
      const templates = getMergedTypeTemplates();
      setTypeTemplates(templates);
    }
  }, [open]);

  // 处理创建Sprite
  const handleCreateSprite = () => {
    if (node) {
      handleCreateNode(NodeType.SPRITE);
    }
    onClose();
  };

  // 处理创建Label
  const handleCreateLabel = () => {
    if (node) {
      handleCreateNode(NodeType.LABEL);
    }
    onClose();
  };

  // 处理创建Container
  const handleCreateContainer = () => {
    if (node) {
      handleCreateNode(NodeType.CONTAINER);
    }
    onClose();
  };

  // 处理创建Window
  const handleCreateWindow = () => {
    if (node) {
      handleCreateNode(NodeType.WINDOW);
    }
    onClose();
  };

  // 处理创建Button
  const handleCreateButton = () => {
    if (node) {
      handleCreateNode(NodeType.BUTTON);
    }
    onClose();
  };

  // 处理创建布局容器
  const handleCreateLayoutContainer = () => {
    if (node) {
      handleCreateNode(NodeType.LAYOUT_CONTAINER);
    }
    onClose();
  };

  // 处理创建自定义类型对象
  const handleCreateCustomType = (typeName: string) => {
    if (node) {
      handleCreateCustomNode(typeName);
    }
    onClose();
  };

  // 处理删除节点
  const handleDeleteNode = useCallback(
    async (nodeToDelete: TreeNode) => {
      if (!nodeToDelete || !nodeToDelete.object) return;

      try {
        // 1. 先获取父对象
        const parent = nodeToDelete.object.parent;

        // 2. 在移除对象之前，先记录对象删除到后端
        await recordObjectDeletion(nodeToDelete.object, parent);

        // 3. 然后再从父对象中移除
        parent.removeChild(nodeToDelete.object);

        // 更新树
        if (updateTree) {
          updateTree();
        }

        // 如果当前选中的是被删除的节点，清除选择
        if (
          selectedObjectsState.objects.some(
            (obj) => obj === nodeToDelete.object
          )
        ) {
          setSelectedObjects([], "object");
        }
      } catch (error) {
        console.error("删除节点失败:", error);
      }
    },
    [updateTree, selectedObjectsState, setSelectedObjects]
  );

  // 处理自定义类型菜单展开/折叠
  const handleCustomTypesToggle = () => {
    setCustomTypesOpen(!customTypesOpen);
  };

  // 创建自定义类型节点
  const handleCreateCustomNode = useCallback(
    async (typeName: string) => {
      if (!node || !node.object) return;

      // 检查父节点是否是容器
      const isContainerNode = checkIsContainer(node.object);
      if (!isContainerNode) {
        console.warn("父节点不是容器，无法添加子节点");
        return;
      }

      // 检查父节点是否有addChild方法
      if (typeof (node.object as any).addChild !== "function") {
        console.warn("父节点没有addChild方法，无法添加子节点");
        return;
      }

      try {
        // 使用tempType中的createObjectByType方法创建对象
        const customObject = createObjectByType(typeName, {
          name: `新${typeName}`,
          x: 0,
          y: 0,
          visible: true,
        });

        if (!customObject) {
          console.error(`创建 ${typeName} 对象失败`);
          return;
        }

        // 如果有尺寸属性，设置默认尺寸
        if ("width" in customObject && "height" in customObject) {
          customObject.width = 50;
          customObject.height = 50;
        }

        // 添加到父容器
        (node.object as any).addChild(customObject);
        console.log(`成功创建并添加 ${typeName} 对象`);

        // 记录对象创建到后端
        try {
          await recordObjectCreation(node.object, customObject, typeName);
          console.log(`已记录 ${typeName} 对象创建到后端`);
        } catch (error) {
          console.error(`记录 ${typeName} 对象创建失败:`, error);
        }

        // 更新树
        if (updateTree) {
          updateTree();
        }
      } catch (error) {
        console.error(`创建 ${typeName} 对象失败:`, error);
      }
    },
    [node, updateTree]
  );

  // 创建节点方法
  const handleCreateNode = useCallback(
    async (type: NodeType) => {
      if (!node || !node.object) return;

      // 检查父节点是否是容器
      const isContainerNode = checkIsContainer(node.object);
      if (!isContainerNode) {
        console.warn("父节点不是容器，无法添加子节点");
        return;
      }

      // 检查父节点是否有addChild方法
      if (typeof (node.object as any).addChild !== "function") {
        console.warn("父节点没有addChild方法，无法添加子节点");
        return;
      }

      try {
        // 根据节点类型获取对应的类型ID
        let typeId: string;
        switch (type) {
          case NodeType.SPRITE:
            typeId = "Sprite";
            break;
          case NodeType.LABEL:
            typeId = "Label";
            break;
          case NodeType.CONTAINER:
            typeId = "Container";
            break;
          case NodeType.WINDOW:
            typeId = "Window";
            break;
          case NodeType.BUTTON:
            typeId = "Button";
            break;
          case NodeType.LAYOUT_CONTAINER:
            typeId = "LayoutContainer";
            break;
          default:
            console.warn("不支持的节点类型");
            return;
        }

        // 使用tempType中的createObjectByType方法创建对象
        const childNode = createObjectByType(typeId, {
          name: `新${typeId}`,
          x: 0,
          y: 0,
          visible: true,
        });

        if (!childNode) {
          console.error(`创建 ${typeId} 对象失败`);
          return;
        }

        // 添加子节点到父容器
        try {
          (node.object as any).addChild(childNode);
          console.log(`成功添加 ${typeId} 对象到父容器`);

          // 记录对象创建到后端
          try {
            await recordObjectCreation(node.object, childNode, typeId);
            console.log(`已记录 ${typeId} 对象创建到后端`);
          } catch (error) {
            console.error(`记录 ${typeId} 对象创建失败:`, error);
          }

          // 更新树
          if (updateTree) {
            updateTree();
          }
        } catch (error) {
          console.error("添加子节点失败:", error);
        }
      } catch (error) {
        console.error("创建节点失败:", error);
      }
    },
    [node, updateTree]
  );

  // 使用typeTemplates替代customTypes

  return (
    <Menu
      open={open}
      onClose={onClose}
      anchorReference="anchorPosition"
      anchorPosition={anchorPosition || undefined}
    >
      <MenuItem onClick={handleCreateSprite}>
        <ListItemIcon>
          <AddPhotoAlternateIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>创建Sprite</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCreateLabel}>
        <ListItemIcon>
          <TextFieldsIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>创建Label</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCreateContainer}>
        <ListItemIcon>
          <FolderIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>创建Container</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCreateWindow}>
        <ListItemIcon>
          <WindowIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>创建Window</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCreateButton}>
        <ListItemIcon>
          <SmartButtonIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText>创建Button</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCreateLayoutContainer}>
        <ListItemIcon>
          <GridViewIcon fontSize="small" color="primary" />
        </ListItemIcon>
        <ListItemText>创建布局容器</ListItemText>
      </MenuItem>

      <MenuItem onClick={handleEnterTypeEdit}>
        <ListItemIcon>
          <EditIcon fontSize="small" color="info" />
        </ListItemIcon>
        <ListItemText>进入类型编辑</ListItemText>
      </MenuItem>
      <Divider />

      <Divider />
      <MenuItem onClick={() => handleDeleteNode(node)}>
        <ListItemIcon>
          <DeleteIcon fontSize="small" color="error" />
        </ListItemIcon>
        <ListItemText>删除</ListItemText>
      </MenuItem>
    </Menu>
  );
};

export default ObjectContextMenuEnhanced;
