import { recordPropertyModification as recordPropertyMod } from './BackendService';
import { getObjectPath as getPath } from '../../utils/object/objectPro';
import { OperationQueue } from '../../generators';

// ==================== 基础属性相关方法 ====================

/**
 * 基础属性类型定义
 */
export type BasicPropertyType =
  | 'position'     // x, y
  | 'size'         // width, height
  | 'transform'    // rotation, skew.x, skew.y, anchor.x, anchor.y
  | 'appearance'   // alpha, tint, color
  | 'border'       // border.width, border.color
  | 'visibility';  // visible

/**
 * 基础属性映射
 */
export const BASIC_PROPERTY_MAP: Record<string, BasicPropertyType> = {
  'x': 'position',
  'y': 'position',
  'width': 'size',
  'height': 'size',
  'rotation': 'transform',
  'skew.x': 'transform',
  'skew.y': 'transform',
  'anchor.x': 'transform',
  'anchor.y': 'transform',
  'alpha': 'appearance',
  'tint': 'appearance',
  'color': 'appearance',
  'border.width': 'border',
  'border.color': 'border',
  'visible': 'visibility'
};

/**
 * 检查是否为基础属性
 */
export const isBasicProperty = (propertyName: string): boolean => {
  return propertyName in BASIC_PROPERTY_MAP;
};

/**
 * 获取基础属性类型
 */
export const getBasicPropertyType = (propertyName: string): BasicPropertyType | null => {
  return BASIC_PROPERTY_MAP[propertyName] || null;
};

/**
 * 处理基础属性修改（前端对象更新）
 * @param objects 要修改的对象数组
 * @param propertyName 属性名称
 * @param value 新值
 * @returns 是否成功修改
 */
export const updateBasicProperty = (objects: any[], propertyName: string, value: any): boolean => {
  if (!isBasicProperty(propertyName)) {
    console.warn(`${propertyName} 不是基础属性`);
    return false;
  }

  try {
    const propertyType = getBasicPropertyType(propertyName);
    console.log(`更新基础属性: ${propertyName} (类型: ${propertyType}) = ${value}`);

    // 获取第一个对象作为参考计算增量
    const firstObject = objects[0];
    if (!firstObject) return false;

    let currentValue = getNestedPropertyValue(firstObject, propertyName);
    let deltaValue = value;

    // 对数值类型计算增量
    if (typeof currentValue === 'number' && typeof value === 'number') {
      deltaValue = value - currentValue;
      console.log(`计算增量值: ${deltaValue} (${value} - ${currentValue})`);
    }

    // 对每个对象应用修改
    for (const object of objects) {
      if (!object) continue;

      if (propertyName.includes('.')) {
        // 处理嵌套属性
        setNestedPropertyValue(object, propertyName, deltaValue, typeof currentValue === 'number');
      } else {
        // 处理直接属性
        if (typeof object[propertyName] === 'number' && typeof deltaValue === 'number') {
          object[propertyName] += deltaValue;
        } else {
          object[propertyName] = deltaValue;
        }
      }

      console.log(`基础属性 ${propertyName} 已更新为: ${getNestedPropertyValue(object, propertyName)}`);
    }

    return true;
  } catch (error) {
    console.error(`更新基础属性 ${propertyName} 失败:`, error);
    return false;
  }
};

/**
 * 记录基础属性修改到后端
 * @param object 要修改的对象
 * @param propertyName 属性名称
 * @param value 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export const recordBasicPropertyModification = async (
  object: any,
  propertyName: string,
  value: any
): Promise<string> => {
  if (!isBasicProperty(propertyName)) {
    throw new Error(`${propertyName} 不是基础属性`);
  }

  const propertyType = getBasicPropertyType(propertyName);
  console.log(`记录基础属性修改: ${propertyName} (类型: ${propertyType}) = ${value}`);

  // 使用统一的后端记录方法
  return await recordPropertyMod(object, propertyName, value);
};

/**
 * 获取嵌套属性值
 */
const getNestedPropertyValue = (obj: any, propertyPath: string): any => {
  const keys = propertyPath.split('.');
  let current = obj;

  for (const key of keys) {
    if (current === null || current === undefined) {
      return undefined;
    }
    current = current[key];
  }

  return current;
};

/**
 * 设置嵌套属性值
 */
const setNestedPropertyValue = (obj: any, propertyPath: string, value: any, isIncrement: boolean = false): void => {
  const keys = propertyPath.split('.');
  let current = obj;

  // 遍历到倒数第二个键
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key]) {
      current[key] = {};
    }
    current = current[key];
  }

  // 设置最后一个键的值
  const lastKey = keys[keys.length - 1];
  if (isIncrement && typeof current[lastKey] === 'number') {
    current[lastKey] += value;
  } else {
    current[lastKey] = value;
  }
};

// ==================== 基础属性管理相关方法 ====================

/**
 * 属性修改信息接口
 */
export interface PropertyModificationInfo {
  objectPath: string[];      // 对象路径
  propertyName: string;      // 属性名称
  oldValue: any;            // 旧值
  newValue: any;            // 新值
  timestamp: number;        // 修改时间戳
}

/**
 * 记录属性修改操作到后端
 * @param object 目标对象
 * @param propertyName 属性名称
 * @param oldValue 旧值
 * @param newValue 新值
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export async function recordPropertyModification(
  object: any,
  propertyName: string,
  oldValue: any,
  newValue: any
): Promise<string> {
  try {
    const modificationInfo: PropertyModificationInfo = {
      objectPath: getPath(object),
      propertyName,
      oldValue,
      newValue,
      timestamp: Date.now()
    };

    console.log(`记录属性修改: ${propertyName} 从 ${oldValue} 改为 ${newValue}`);

    // 使用统一的后端记录方法
    return await recordPropertyMod(
      object,
      propertyName,
      newValue
    );
  } catch (error) {
    console.error('记录属性修改失败:', error);
    throw error;
  }
}

/**
 * 验证属性修改操作
 * @param object 目标对象
 * @param propertyName 属性名称
 * @param newValue 新值
 * @returns boolean 是否有效
 */
export function validatePropertyModification(
  object: any,
  propertyName: string,
  newValue: any
): boolean {
  if (!object) {
    console.error('目标对象不能为空');
    return false;
  }

  if (!propertyName) {
    console.error('属性名称不能为空');
    return false;
  }

  // 检查属性是否可写
  if (typeof object[propertyName] === 'undefined') {
    console.error(`属性 ${propertyName} 不存在`);
    return false;
  }

  // 检查属性类型是否匹配
  const currentType = typeof object[propertyName];
  const newType = typeof newValue;
  if (currentType !== newType) {
    console.error(`属性类型不匹配: 期望 ${currentType}，实际 ${newType}`);
    return false;
  }

  return true;
}

/**
 * 获取属性默认值
 * @param propertyName 属性名称
 * @returns any 默认值
 */
export function getPropertyDefaultValue(propertyName: string): any {
  const defaults: Record<string, any> = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    visible: true,
    alpha: 1,
    rotation: 0,
    scale: { x: 1, y: 1 },
    anchor: { x: 0.5, y: 0.5 },
    tint: 0xFFFFFF,
    blendMode: 0,
    filters: [],
    mask: null,
    interactive: false,
    buttonMode: false,
    cursor: 'pointer',
    hitArea: null,
    eventMode: 'none',
    sortableChildren: false,
    sortDirty: false,
    zIndex: 0,
    zOrder: 0,
    z: 0,
    name: '',
    text: '',
    fontSize: 24,
    fontFamily: 'Arial',
    fontStyle: 'normal',
    fontWeight: 'normal',
    fill: 0x000000,
    align: 'left',
    wordWrap: false,
    wordWrapWidth: 0,
    lineHeight: 0,
    letterSpacing: 0,
    textBaseline: 'alphabetic',
    dropShadow: false,
    dropShadowColor: 0x000000,
    dropShadowBlur: 0,
    dropShadowAngle: 0,
    dropShadowDistance: 0,
    padding: 0,
    textShadow: false,
    textShadowColor: 0x000000,
    textShadowBlur: 0,
    textShadowAngle: 0,
    textShadowDistance: 0
  };

  return defaults[propertyName];
}

/**
 * 获取属性类型
 * @param propertyName 属性名称
 * @returns string 属性类型
 */
export function getPropertyType(propertyName: string): string {
  const types: Record<string, string> = {
    x: 'number',
    y: 'number',
    width: 'number',
    height: 'number',
    visible: 'boolean',
    alpha: 'number',
    rotation: 'number',
    scale: 'object',
    anchor: 'object',
    tint: 'number',
    blendMode: 'number',
    filters: 'array',
    mask: 'object',
    interactive: 'boolean',
    buttonMode: 'boolean',
    cursor: 'string',
    hitArea: 'object',
    eventMode: 'string',
    sortableChildren: 'boolean',
    sortDirty: 'boolean',
    zIndex: 'number',
    zOrder: 'number',
    z: 'number',
    name: 'string',
    text: 'string',
    fontSize: 'number',
    fontFamily: 'string',
    fontStyle: 'string',
    fontWeight: 'string',
    fill: 'number',
    align: 'string',
    wordWrap: 'boolean',
    wordWrapWidth: 'number',
    lineHeight: 'number',
    letterSpacing: 'number',
    textBaseline: 'string',
    dropShadow: 'boolean',
    dropShadowColor: 'number',
    dropShadowBlur: 'number',
    dropShadowAngle: 'number',
    dropShadowDistance: 'number',
    padding: 'number',
    textShadow: 'boolean',
    textShadowColor: 'number',
    textShadowBlur: 'number',
    textShadowAngle: 'number',
    textShadowDistance: 'number'
  };

  return types[propertyName] || 'unknown';
}

/**
 * 获取属性描述
 * @param propertyName 属性名称
 * @returns string 属性描述
 */
export function getPropertyDescription(propertyName: string): string {
  const descriptions: Record<string, string> = {
    x: 'X坐标',
    y: 'Y坐标',
    width: '宽度',
    height: '高度',
    visible: '是否可见',
    alpha: '透明度',
    rotation: '旋转角度',
    scale: '缩放比例',
    anchor: '锚点',
    tint: '颜色',
    blendMode: '混合模式',
    filters: '滤镜',
    mask: '遮罩',
    interactive: '是否可交互',
    buttonMode: '按钮模式',
    cursor: '鼠标样式',
    hitArea: '点击区域',
    eventMode: '事件模式',
    sortableChildren: '子对象排序',
    sortDirty: '排序标记',
    zIndex: 'Z轴索引',
    zOrder: 'Z轴顺序',
    z: 'Z轴位置',
    name: '名称',
    text: '文本内容',
    fontSize: '字体大小',
    fontFamily: '字体',
    fontStyle: '字体样式',
    fontWeight: '字体粗细',
    fill: '填充颜色',
    align: '对齐方式',
    wordWrap: '自动换行',
    wordWrapWidth: '换行宽度',
    lineHeight: '行高',
    letterSpacing: '字间距',
    textBaseline: '文本基线',
    dropShadow: '阴影',
    dropShadowColor: '阴影颜色',
    dropShadowBlur: '阴影模糊',
    dropShadowAngle: '阴影角度',
    dropShadowDistance: '阴影距离',
    padding: '内边距',
    textShadow: '文本阴影',
    textShadowColor: '文本阴影颜色',
    textShadowBlur: '文本阴影模糊',
    textShadowAngle: '文本阴影角度',
    textShadowDistance: '文本阴影距离'
  };

  return descriptions[propertyName] || propertyName;
}

/**
 * 获取属性分类
 * @param propertyName 属性名称
 * @returns string 属性分类
 */
export function getPropertyCategory(propertyName: string): string {
  const categories: Record<string, string> = {
    x: 'transform',
    y: 'transform',
    width: 'size',
    height: 'size',
    visible: 'display',
    alpha: 'display',
    rotation: 'transform',
    scale: 'transform',
    anchor: 'transform',
    tint: 'display',
    blendMode: 'display',
    filters: 'display',
    mask: 'display',
    interactive: 'interaction',
    buttonMode: 'interaction',
    cursor: 'interaction',
    hitArea: 'interaction',
    eventMode: 'interaction',
    sortableChildren: 'layout',
    sortDirty: 'layout',
    zIndex: 'layout',
    zOrder: 'layout',
    z: 'layout',
    name: 'basic',
    text: 'text',
    fontSize: 'text',
    fontFamily: 'text',
    fontStyle: 'text',
    fontWeight: 'text',
    fill: 'text',
    align: 'text',
    wordWrap: 'text',
    wordWrapWidth: 'text',
    lineHeight: 'text',
    letterSpacing: 'text',
    textBaseline: 'text',
    dropShadow: 'text',
    dropShadowColor: 'text',
    dropShadowBlur: 'text',
    dropShadowAngle: 'text',
    dropShadowDistance: 'text',
    padding: 'text',
    textShadow: 'text',
    textShadowColor: 'text',
    textShadowBlur: 'text',
    textShadowAngle: 'text',
    textShadowDistance: 'text'
  };

  return categories[propertyName] || 'other';
}
