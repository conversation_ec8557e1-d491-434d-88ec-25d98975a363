/**
 * Bitmap属性代码生成器
 * 处理_bitmap相关属性、text、texture等
 * 参考后端bitmap_generator.rs的实现逻辑
 */

import { PropertyModificationInfo, CodeSnippet, PropertyChange } from '../core/types';
import { PropertyGeneratorHandler } from './PropertyGenerator';

export class BitmapGenerator implements PropertyGeneratorHandler {
  
  /**
   * 检查是否可以处理该属性
   */
  canHandle(propertyName: string, objectType: string): boolean {
    return propertyName.startsWith('_bitmap.') || 
           propertyName === 'text' || 
           propertyName === 'texture' ||
           propertyName === 'bitmap._url';
  }

  /**
   * 生成bitmap相关代码
   */
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { objectPath, className, properties } = operation;
    const variableName = this.getObjectVariableName(objectPath, className);

    // 收集所有bitmap相关属性
    const bitmapProperties = new Map<string, PropertyChange>();
    
    if (properties) {
      for (const [propertyName, propertyChange] of properties) {
        if (this.canHandle(propertyName, className)) {
          bitmapProperties.set(propertyName, propertyChange);
        }
      }
    }

    // 兼容性：处理单属性格式
    if (operation.propertyName && this.canHandle(operation.propertyName, className)) {
      const propertyChange: PropertyChange = {
        propertyName: operation.propertyName,
        oldValue: operation.oldValue,
        newValue: operation.newValue,
        propertyType: operation.propertyType,
        timestamp: operation.timestamp
      };
      bitmapProperties.set(operation.propertyName, propertyChange);
    }

    if (bitmapProperties.size === 0) {
      return {
        code: '',
        description: 'No bitmap properties to process',
        dependencies: []
      };
    }

    // 生成完整的bitmap处理代码
    return this.generateBitmapCode(variableName, objectPath, className, bitmapProperties);
  }

  /**
   * 生成bitmap代码的主要逻辑
   */
  private generateBitmapCode(
    variableName: string, 
    objectPath: string[], 
    className: string,
    properties: Map<string, PropertyChange>
  ): CodeSnippet {
    const lines: string[] = [];
    const dependencies: string[] = ['findObjectByScenePath'];

    // 生成对象查找代码
    lines.push(...this.generateObjectLookup(objectPath, variableName));

    // 检查是否有不同类型的bitmap操作
    const hasElementsModifications = Array.from(properties.keys()).some(k => k.startsWith('_bitmap.elements'));
    const hasImageElements = this.checkForImageElements(properties);
    const hasTextureChange = properties.has('texture') || properties.has('bitmap._url');

    lines.push(`if (${variableName}) {`);

    // 处理纹理变更（优先级最高）
    if (hasTextureChange) {
      lines.push(...this.generateTextureCode(variableName, properties));
    }
    // 处理bitmap相关属性
    else if (Array.from(properties.keys()).some(k => k.startsWith('_bitmap.') || k === 'text')) {
      lines.push(`    // 合并处理所有_bitmap相关属性`);
      lines.push(`    if (${variableName}._bitmap) {`);

      if (hasElementsModifications) {
        lines.push(...this.generateElementsModificationCode(variableName, properties));
      } else if (hasImageElements) {
        lines.push(...this.generateImageLoadingCode(variableName, properties));
      } else {
        lines.push(...this.generateTextRedrawCode(variableName, properties));
      }

      lines.push(`    }`);
    }

    lines.push('}');

    return {
      code: lines.join('\n'),
      description: `处理 ${className} 的 ${properties.size} 个bitmap属性`,
      dependencies,
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName,
        propertyCount: properties.size,
        hasTextureChange,
        hasElementsModifications,
        hasImageElements
      }
    };
  }

  /**
   * 生成纹理设置代码
   */
  private generateTextureCode(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];

    for (const [propertyName, propertyChange] of properties) {
      if (propertyName === 'texture') {
        const imagePath = propertyChange.newValue as string;
        lines.push(`    // 设置纹理`);
        lines.push(`    const newBitmap = ImageManager.loadBitmapFromUrl("${imagePath}");`);
        lines.push(`    newBitmap.addLoadListener(function (bitmap) {`);
        lines.push(`        ${variableName}.bitmap = bitmap;`);
        lines.push(`        if (DEBUG) console.log('设置纹理:', "${imagePath}", ${variableName});`);
        lines.push(`    });`);
      } else if (propertyName === 'bitmap._url') {
        const imagePath = propertyChange.newValue as string;
        lines.push(`    // 更新 PIXI.js Sprite 纹理`);
        lines.push(`    const newBitmap = ImageManager.loadBitmapFromUrl("${imagePath}");`);
        lines.push(`    newBitmap.addLoadListener(function (bitmap) {`);
        lines.push(`        ${variableName}.bitmap = bitmap;`);
        lines.push(`    });`);
        lines.push(`    if (DEBUG) console.log('设置 Sprite 纹理 = ', "${imagePath}");`);
      }
    }

    return lines;
  }

  /**
   * 生成文本重绘代码
   */
  private generateTextRedrawCode(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];

    // 设置所有_bitmap属性
    for (const [propertyName, propertyChange] of properties) {
      if (propertyName.startsWith('_bitmap.')) {
        const bitmapProp = propertyName.replace('_bitmap.', '');
        const valueStr = this.serializeValue(propertyChange.newValue);
        lines.push(`        // 设置 ${bitmapProp} 属性`);
        lines.push(`        ${variableName}._bitmap.${bitmapProp} = ${valueStr};`);
        lines.push(`        if (DEBUG) console.log('设置文字样式 ${bitmapProp} =', ${valueStr});`);
      } else if (propertyName === 'text') {
        const valueStr = this.serializeValue(propertyChange.newValue);
        lines.push(`        // 设置文本内容`);
        lines.push(`        ${variableName}.text = ${valueStr};`);
        lines.push(`        if (DEBUG) console.log('设置文本内容 =', ${valueStr});`);
      }
    }

    // 触发重绘
    lines.push(`        // 触发文本重绘`);
    lines.push(`        if (${variableName}.refresh) {`);
    lines.push(`            ${variableName}.refresh();`);
    lines.push(`        }`);

    return lines;
  }

  /**
   * 生成elements修改代码
   */
  private generateElementsModificationCode(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];

    // 处理整个数组的替换
    const elementsProperty = properties.get('_bitmap.elements');
    if (elementsProperty) {
      const hasImageElements = this.checkElementsForImages(elementsProperty.newValue);
      
      if (hasImageElements) {
        lines.push(...this.generateElementsWithImagesCode(variableName, elementsProperty.newValue));
      } else {
        const valueStr = this.serializeValue(elementsProperty.newValue);
        lines.push(`        // 设置整个 elements 数组（仅文本）`);
        lines.push(`        ${variableName}._bitmap.elements = ${valueStr};`);
        lines.push(`        if (DEBUG) console.log('设置 _bitmap.elements 数组，长度:', ${variableName}._bitmap.elements.length);`);
      }
    }

    // 处理数组元素的具体属性修改
    for (const [propertyName, propertyChange] of properties) {
      if (propertyName.startsWith('_bitmap.elements[') && propertyName !== '_bitmap.elements') {
        const valueStr = this.serializeValue(propertyChange.newValue);
        const bitmapProp = propertyName.replace('_bitmap.', '');
        
        lines.push(`        // 设置 ${bitmapProp} 属性`);
        const arrayIndex = bitmapProp.split('.')[0]; // 提取 elements[0] 部分
        lines.push(`        if (${variableName}._bitmap.${arrayIndex}) {`);
        lines.push(`            ${variableName}._bitmap.${bitmapProp} = ${valueStr};`);
        lines.push(`            if (DEBUG) console.log('设置 _bitmap.${bitmapProp} =', ${valueStr});`);
        lines.push(`        }`);
      }
    }

    return lines;
  }

  /**
   * 生成图片加载代码
   */
  private generateImageLoadingCode(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];
    // 这里可以根据需要实现图片加载逻辑
    // 参考后端的generate_image_loading_code实现
    lines.push(`        // 图片加载逻辑待实现`);
    return lines;
  }

  /**
   * 生成包含图片的elements代码
   */
  private generateElementsWithImagesCode(variableName: string, elementsValue: any): string[] {
    const lines: string[] = [];
    // 这里可以根据需要实现包含图片的elements处理逻辑
    // 参考后端的generate_elements_with_images_code实现
    lines.push(`        // 包含图片的elements处理逻辑待实现`);
    return lines;
  }

  /**
   * 检查是否有图片元素
   */
  private checkForImageElements(properties: Map<string, PropertyChange>): boolean {
    // 简化实现，可以根据需要扩展
    return false;
  }

  /**
   * 检查elements数组中是否包含图片
   */
  private checkElementsForImages(elementsValue: any): boolean {
    if (!Array.isArray(elementsValue)) return false;
    return elementsValue.some((element: any) => element && element.type === 'image');
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookup(objectPath: string[], variableName: string): string[] {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ];
  }

  /**
   * 生成对象变量名
   */
  private getObjectVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 序列化值为JavaScript代码
   */
  private serializeValue(value: any): string {
    if (typeof value === 'string') {
      return `"${value.replace(/"/g, '\\"')}"`;
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value);
    } else if (Array.isArray(value)) {
      return `[${value.map(v => this.serializeValue(v)).join(', ')}]`;
    } else if (value === null || value === undefined) {
      return 'null';
    } else {
      return JSON.stringify(value);
    }
  }
}
