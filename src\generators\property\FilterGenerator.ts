/**
 * 滤镜属性代码生成器
 * 处理filters、filters_operation等滤镜相关属性
 * 参考后端filter_generator.rs的实现逻辑
 */

import { PropertyModificationInfo, CodeSnippet, PropertyChange } from '../core/types';
import { PropertyGeneratorHandler } from './PropertyGenerator';

export class FilterGenerator implements PropertyGeneratorHandler {
  
  /**
   * 检查是否可以处理该属性
   */
  canHandle(propertyName: string, objectType: string): boolean {
    return propertyName.startsWith('filters') || 
           propertyName.includes('filters_operation') ||
           propertyName.includes('filter_param');
  }

  /**
   * 生成滤镜相关代码
   */
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { objectPath, className, properties } = operation;
    const variableName = this.getObjectVariableName(objectPath, className);

    // 收集所有滤镜相关属性
    const filterProperties = new Map<string, PropertyChange>();
    
    if (properties) {
      for (const [propertyName, propertyChange] of properties) {
        if (this.canHandle(propertyName, className)) {
          filterProperties.set(propertyName, propertyChange);
        }
      }
    }

    // 兼容性：处理单属性格式
    if (operation.propertyName && this.canHandle(operation.propertyName, className)) {
      const propertyChange: PropertyChange = {
        propertyName: operation.propertyName,
        oldValue: operation.oldValue,
        newValue: operation.newValue,
        propertyType: operation.propertyType,
        timestamp: operation.timestamp
      };
      filterProperties.set(operation.propertyName, propertyChange);
    }

    if (filterProperties.size === 0) {
      return {
        code: '',
        description: 'No filter properties to process',
        dependencies: []
      };
    }

    // 生成完整的滤镜处理代码
    return this.generateFilterCode(variableName, objectPath, className, filterProperties);
  }

  /**
   * 生成滤镜代码的主要逻辑
   */
  private generateFilterCode(
    variableName: string, 
    objectPath: string[], 
    className: string,
    properties: Map<string, PropertyChange>
  ): CodeSnippet {
    const lines: string[] = [];
    const dependencies: string[] = ['findObjectByScenePath'];

    // 生成对象查找代码
    lines.push(...this.generateObjectLookup(objectPath, variableName));

    lines.push(`if (${variableName}) {`);

    // 分类处理不同类型的滤镜操作
    const filterOperations = this.extractFilterOperations(properties);
    const filterParamModifications = this.extractFilterParamModifications(properties);
    const directFiltersAssignment = properties.get('filters');

    // 检查直接的filters数组赋值（通常是错误的）
    if (directFiltersAssignment) {
      lines.push(`    // 跳过直接的 filters 数组赋值（应该使用滤镜操作）`);
      lines.push(`    if (DEBUG) console.log('警告: 检测到直接的 filters 数组赋值，这可能导致问题');`);
    }

    // 处理滤镜操作（添加、删除、重排）
    if (filterOperations.length > 0) {
      lines.push(...this.generateFilterOperationsCode(variableName, filterOperations));
    }

    // 处理滤镜参数修改
    if (filterParamModifications.length > 0) {
      lines.push(...this.generateFilterParamModificationsCode(variableName, filterParamModifications));
    }

    lines.push('}');

    return {
      code: lines.join('\n'),
      description: `处理 ${className} 的 ${properties.size} 个滤镜属性`,
      dependencies,
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName,
        propertyCount: properties.size,
        hasOperations: filterOperations.length > 0,
        hasParamModifications: filterParamModifications.length > 0
      }
    };
  }

  /**
   * 提取滤镜操作
   */
  private extractFilterOperations(properties: Map<string, PropertyChange>): Array<[string, PropertyChange]> {
    const operations: Array<[string, PropertyChange]> = [];
    
    for (const [propertyName, propertyChange] of properties) {
      if (propertyName.includes('filters_operation')) {
        operations.push([propertyName, propertyChange]);
      }
    }
    
    return operations;
  }

  /**
   * 提取滤镜参数修改
   */
  private extractFilterParamModifications(properties: Map<string, PropertyChange>): Array<[string, PropertyChange]> {
    const modifications: Array<[string, PropertyChange]> = [];
    
    for (const [propertyName, propertyChange] of properties) {
      if (propertyName.includes('filter_param') || 
          (propertyName.startsWith('filters[') && !propertyName.includes('filters_operation'))) {
        modifications.push([propertyName, propertyChange]);
      }
    }
    
    return modifications;
  }

  /**
   * 生成滤镜操作代码
   */
  private generateFilterOperationsCode(variableName: string, operations: Array<[string, PropertyChange]>): string[] {
    const lines: string[] = [];
    
    lines.push(`    // 执行滤镜操作`);
    
    for (const [propertyName, propertyChange] of operations) {
      const operationData = propertyChange.newValue;
      
      if (typeof operationData === 'object' && operationData !== null) {
        const { operation, filterType, params, index } = operationData;
        
        switch (operation) {
          case 'add':
            lines.push(...this.generateAddFilterCode(variableName, filterType, params, index));
            break;
          case 'remove':
            lines.push(...this.generateRemoveFilterCode(variableName, index));
            break;
          case 'reorder':
            lines.push(...this.generateReorderFilterCode(variableName, operationData));
            break;
        }
      }
    }
    
    return lines;
  }

  /**
   * 生成添加滤镜代码
   */
  private generateAddFilterCode(variableName: string, filterType: string, params: any, insertIndex?: number): string[] {
    const lines: string[] = [];
    
    switch (filterType) {
      case 'blur':
        const blur = params?.blur || 2.0;
        const quality = params?.quality || 4;
        
        lines.push(`    const newBlurFilter = new PIXI.filters.BlurFilter(${blur}, ${quality});`);
        
        if (insertIndex !== undefined) {
          lines.push(`    ${variableName}.filters.splice(${insertIndex}, 0, newBlurFilter);`);
        } else {
          lines.push(`    ${variableName}.filters.push(newBlurFilter);`);
        }
        
        lines.push(`    if (DEBUG) console.log('添加模糊滤镜: blur=${blur}, quality=${quality}');`);
        break;
        
      case 'alpha':
        const alpha = params?.alpha || 1.0;
        
        lines.push(`    const newAlphaFilter = new PIXI.filters.AlphaFilter(${alpha});`);
        
        if (insertIndex !== undefined) {
          lines.push(`    ${variableName}.filters.splice(${insertIndex}, 0, newAlphaFilter);`);
        } else {
          lines.push(`    ${variableName}.filters.push(newAlphaFilter);`);
        }
        
        lines.push(`    if (DEBUG) console.log('添加透明度滤镜: alpha=${alpha}');`);
        break;
        
      default:
        // 处理自定义滤镜
        lines.push(...this.generateCustomFilterCode(variableName, filterType, params, insertIndex));
        break;
    }
    
    return lines;
  }

  /**
   * 生成删除滤镜代码
   */
  private generateRemoveFilterCode(variableName: string, index: number): string[] {
    return [
      `    // 删除滤镜`,
      `    if (${variableName}.filters && ${variableName}.filters[${index}]) {`,
      `        ${variableName}.filters.splice(${index}, 1);`,
      `        if (DEBUG) console.log('删除滤镜，索引:', ${index});`,
      `    }`
    ];
  }

  /**
   * 生成重排滤镜代码
   */
  private generateReorderFilterCode(variableName: string, operationData: any): string[] {
    const { fromIndex, toIndex } = operationData;
    
    return [
      `    // 重排滤镜`,
      `    if (${variableName}.filters && ${variableName}.filters[${fromIndex}]) {`,
      `        const filter = ${variableName}.filters.splice(${fromIndex}, 1)[0];`,
      `        ${variableName}.filters.splice(${toIndex}, 0, filter);`,
      `        if (DEBUG) console.log('重排滤镜: 从索引', ${fromIndex}, '到索引', ${toIndex});`,
      `    }`
    ];
  }

  /**
   * 生成自定义滤镜代码
   */
  private generateCustomFilterCode(variableName: string, filterType: string, params: any, insertIndex?: number): string[] {
    const lines: string[] = [];
    
    lines.push(`    // 添加自定义滤镜: ${filterType}`);
    lines.push(`    try {`);
    lines.push(`        const customFilter = new PIXI.filters.${filterType}Filter(${JSON.stringify(params)});`);
    
    if (insertIndex !== undefined) {
      lines.push(`        ${variableName}.filters.splice(${insertIndex}, 0, customFilter);`);
    } else {
      lines.push(`        ${variableName}.filters.push(customFilter);`);
    }
    
    lines.push(`        if (DEBUG) console.log('添加自定义滤镜:', '${filterType}', ${JSON.stringify(params)});`);
    lines.push(`    } catch (error) {`);
    lines.push(`        console.error('创建自定义滤镜失败:', error);`);
    lines.push(`    }`);
    
    return lines;
  }

  /**
   * 生成滤镜参数修改代码
   */
  private generateFilterParamModificationsCode(variableName: string, modifications: Array<[string, PropertyChange]>): string[] {
    const lines: string[] = [];
    
    lines.push(`    // 修改滤镜参数`);
    
    for (const [propertyName, propertyChange] of modifications) {
      const value = propertyChange.newValue;
      const valueStr = this.serializeValue(value);
      
      // 解析属性路径，如 filters[0].blur 或 filter_param_0_blur
      const { filterIndex, paramName } = this.parseFilterParamPath(propertyName);
      
      if (filterIndex !== null && paramName) {
        lines.push(`    // 修改滤镜 ${filterIndex} 的参数 ${paramName}`);
        lines.push(`    if (${variableName}.filters && ${variableName}.filters[${filterIndex}]) {`);
        
        // 处理颜色数组参数
        if (paramName.includes('Color_')) {
          lines.push(...this.generateColorParamCode(variableName, filterIndex, paramName, value));
        } else {
          lines.push(`        ${variableName}.filters[${filterIndex}].${paramName} = ${valueStr};`);
        }
        
        lines.push(`        if (DEBUG) console.log('修改滤镜参数:', '${paramName}', ${valueStr});`);
        lines.push(`    }`);
      }
    }
    
    return lines;
  }

  /**
   * 解析滤镜参数路径
   */
  private parseFilterParamPath(propertyName: string): { filterIndex: number | null, paramName: string | null } {
    // 处理 filters[0].blur 格式
    const filtersMatch = propertyName.match(/filters\[(\d+)\]\.(.+)/);
    if (filtersMatch) {
      return {
        filterIndex: parseInt(filtersMatch[1]),
        paramName: filtersMatch[2]
      };
    }
    
    // 处理 filter_param_0_blur 格式
    const paramMatch = propertyName.match(/filter_param_(\d+)_(.+)/);
    if (paramMatch) {
      return {
        filterIndex: parseInt(paramMatch[1]),
        paramName: paramMatch[2]
      };
    }
    
    return { filterIndex: null, paramName: null };
  }

  /**
   * 生成颜色参数代码
   */
  private generateColorParamCode(variableName: string, filterIndex: number, paramName: string, value: any): string[] {
    const lines: string[] = [];
    
    // 处理颜色数组参数，如 fireColor_r, glowColor_g 等
    const colorType = paramName.startsWith('fireColor_') ? 'fireColor' :
                     paramName.startsWith('glowColor_') ? 'glowColor' : 'color';
    
    const component = paramName.endsWith('_r') ? 0 :
                     paramName.endsWith('_g') ? 1 : 2;
    
    lines.push(`        if (${variableName}.filters[${filterIndex}].uniforms && ${variableName}.filters[${filterIndex}].uniforms.${colorType}) {`);
    lines.push(`            ${variableName}.filters[${filterIndex}].uniforms.${colorType}[${component}] = ${value};`);
    lines.push(`        }`);
    
    return lines;
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookup(objectPath: string[], variableName: string): string[] {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ];
  }

  /**
   * 生成对象变量名
   */
  private getObjectVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 序列化值为JavaScript代码
   */
  private serializeValue(value: any): string {
    if (typeof value === 'string') {
      return `"${value.replace(/"/g, '\\"')}"`;
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value);
    } else if (Array.isArray(value)) {
      return `[${value.map(v => this.serializeValue(v)).join(', ')}]`;
    } else if (value === null || value === undefined) {
      return 'null';
    } else {
      return JSON.stringify(value);
    }
  }
}
