/**
 * 调试字符分离问题
 * 用于重现和修复字符被分离成单个字符的问题
 */

// 测试可能导致字符分离的操作
export function debugCharacterSeparation(): void {
  console.log('开始调试字符分离问题...');

  // 测试1: 正常的字符串操作
  const normalString = "const target_sprite_Scene_Title_2Path = [\"Scene_Title\", \"2\"];";
  console.log('正常字符串:', normalString);

  // 测试2: 错误的spread操作
  const wrongSpread = [...normalString]; // 这会将字符串分离成字符数组
  console.log('错误的spread操作结果:', wrongSpread);
  console.log('错误的spread操作join结果:', wrongSpread.join('\n'));

  // 测试3: 正确的数组操作
  const correctArray = [
    "const target_sprite_Scene_Title_2Path = [\"Scene_Title\", \"2\"];",
    "const target_sprite_Scene_Title_2 = findObjectByScenePath(target_sprite_Scene_Title_2Path);"
  ];
  console.log('正确的数组操作结果:', correctArray.join('\n'));

  // 测试4: 检查是否有地方错误地处理了字符串
  function testStringProcessing(input: any): string {
    if (typeof input === 'string') {
      return input;
    } else if (Array.isArray(input)) {
      return input.join('\n');
    } else {
      return String(input);
    }
  }

  console.log('字符串处理测试1:', testStringProcessing(normalString));
  console.log('字符串处理测试2:', testStringProcessing(correctArray));

  // 测试5: 模拟可能的错误场景
  function simulateError(code: string | string[]): string {
    // 错误的处理方式：将字符串当作数组处理
    if (typeof code === 'string') {
      // 这是错误的！会将字符串分离成字符
      const wrongResult = [...code].join('\n');
      console.log('模拟错误结果:', wrongResult.substring(0, 100) + '...');
      return wrongResult;
    } else {
      return code.join('\n');
    }
  }

  simulateError(normalString);

  // 测试6: 检查可能的序列化问题
  const testObject = {
    code: normalString,
    lines: correctArray
  };

  console.log('对象序列化测试:', JSON.stringify(testObject, null, 2));

  // 测试7: 检查可能的编码问题
  const encoded = encodeURIComponent(normalString);
  const decoded = decodeURIComponent(encoded);
  console.log('编码解码测试:', decoded === normalString ? '正常' : '异常');

  console.log('调试完成！');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  debugCharacterSeparation();
}
