/**
 * 测试与后端对齐的bitmap.elements处理
 * 验证是否与后端bitmap_generator.rs逻辑一致
 */

import { PropertyGenerator } from '../property/PropertyGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建与后端一致的测试操作
function createBackendAlignedTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  const elementsValue = [
    {
      "type": "text",
      "text": "里德",
      "x": 184,
      "y": 13,
      "maxWidth": 168,
      "lineHeight": 36,
      "align": "left",
      "bounds": {
        "x": 184,
        "y": 13,
        "width": 168,
        "height": 36
      }
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/faces/Actor1.png",
        "width": 144,
        "height": 129
      },
      "sx": 432,
      "sy": 7,
      "sw": 144,
      "sh": 129,
      "dx": 5,
      "dy": 138,
      "dw": 144,
      "dh": 129,
      "bounds": {
        "x": 5,
        "y": 138,
        "width": 144,
        "height": 129
      }
    }
  ];
  
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: Date.now()
  });

  return {
    id: 'backend_aligned_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Menu', '1', '3', '1', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
}

// 分析生成的代码是否与后端一致
function analyzeBackendAlignment(code: string): {
  isAligned: boolean;
  issues: string[];
  details: string[];
} {
  const lines = code.split('\n');
  const issues: string[] = [];
  const details: string[] = [];
  
  // 检查是否直接设置了完整的elements数组（包含source）
  const hasDirectElementsAssignment = code.includes('_bitmap.elements = [') && code.includes('"source":{"_url"');
  const hasCompleteSourceInfo = code.includes('"source":{"_url"') && code.includes('"width"') && code.includes('"height"');
  
  // 检查是否有正确的图片加载逻辑
  const hasImageLoading = code.includes('ImageManager.loadBitmapFromUrl');
  const hasSourceBitmapSetting = code.includes('.source.bitmap = bitmap');
  const hasAddLoadListener = code.includes('addLoadListener');
  
  // 检查是否没有错误的source分离逻辑
  const hasNoSourceSeparation = !code.includes('不包含图片source') && !code.includes('创建不包含source的元素副本');
  
  // 检查变量名是否正确
  const expectedVariableName = 'target_sprite_Scene_Menu_1_3_1_2';
  const hasCorrectVariableName = code.includes(expectedVariableName);
  const hasIncorrectVariableName = code.includes('target_sprite_temp') || code.includes('target_sprite_.');
  
  // 检查大括号匹配
  let braceCount = 0;
  lines.forEach(line => {
    const openBraces = (line.match(/\{/g) || []).length;
    const closeBraces = (line.match(/\}/g) || []).length;
    braceCount += openBraces - closeBraces;
  });
  const hasBracketMatch = braceCount === 0;
  
  // 验证结果
  if (!hasDirectElementsAssignment) {
    issues.push('没有直接设置完整的elements数组');
  }
  if (!hasCompleteSourceInfo) {
    issues.push('elements数组中缺少完整的source信息');
  }
  if (!hasImageLoading) {
    issues.push('缺少图片加载逻辑');
  }
  if (!hasSourceBitmapSetting) {
    issues.push('缺少source.bitmap设置');
  }
  if (!hasAddLoadListener) {
    issues.push('缺少addLoadListener回调');
  }
  if (!hasNoSourceSeparation) {
    issues.push('仍然存在错误的source分离逻辑');
  }
  if (!hasCorrectVariableName) {
    issues.push('缺少正确的变量名');
  }
  if (hasIncorrectVariableName) {
    issues.push('存在错误的变量名');
  }
  if (!hasBracketMatch) {
    issues.push('大括号不匹配');
  }
  
  // 详细信息
  details.push(`直接设置完整数组: ${hasDirectElementsAssignment ? '✅' : '❌'}`);
  details.push(`包含完整source信息: ${hasCompleteSourceInfo ? '✅' : '❌'}`);
  details.push(`图片加载逻辑: ${hasImageLoading ? '✅' : '❌'}`);
  details.push(`source.bitmap设置: ${hasSourceBitmapSetting ? '✅' : '❌'}`);
  details.push(`addLoadListener回调: ${hasAddLoadListener ? '✅' : '❌'}`);
  details.push(`无错误source分离: ${hasNoSourceSeparation ? '✅' : '❌'}`);
  details.push(`正确变量名: ${hasCorrectVariableName ? '✅' : '❌'}`);
  details.push(`无错误变量名: ${!hasIncorrectVariableName ? '✅' : '❌'}`);
  details.push(`大括号匹配: ${hasBracketMatch ? '✅' : '❌'}`);
  
  return {
    isAligned: issues.length === 0,
    issues,
    details
  };
}

export function testBackendAlignedBitmap(): void {
  console.log('🔧 测试与后端对齐的bitmap.elements处理...');
  
  const generator = new PropertyGenerator();
  const testOperation = createBackendAlignedTestOperation();
  
  try {
    const result = generator.generate(testOperation);
    
    console.log('生成的代码:');
    console.log('='.repeat(80));
    console.log(result.code);
    console.log('='.repeat(80));
    
    // 分析与后端的对齐情况
    const analysis = analyzeBackendAlignment(result.code);
    
    console.log('\n🔍 与后端对齐分析:');
    analysis.details.forEach(detail => {
      console.log(`- ${detail}`);
    });
    
    if (analysis.issues.length > 0) {
      console.log('\n❌ 发现的问题:');
      analysis.issues.forEach(issue => {
        console.log(`- ${issue}`);
      });
    }
    
    console.log('\n🎯 总体结果:', analysis.isAligned ? '✅ 完全与后端对齐！' : '❌ 与后端不一致');
    
    if (analysis.isAligned) {
      console.log('\n🎉 恭喜！前端代码生成已完全与后端逻辑对齐！');
      console.log('✅ 直接设置完整的elements数组（包含source）');
      console.log('✅ 为图片元素生成bitmap加载代码');
      console.log('✅ 在加载完成后只设置source.bitmap');
      console.log('✅ 没有错误的source分离逻辑');
      console.log('✅ 变量名和大括号都正确');
    } else {
      console.log('\n🔧 需要修复的问题:');
      analysis.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    // 与期望的后端格式对比
    console.log('\n📋 期望的后端格式:');
    console.log('1. 直接设置完整的elements数组：target._bitmap.elements = [完整数组包含source];');
    console.log('2. 为每个图片元素生成加载代码：ImageManager.loadBitmapFromUrl(path);');
    console.log('3. 在加载完成后设置bitmap：element.source.bitmap = bitmap;');
    console.log('4. 不分离source，不创建临时数组');
    
    // 检查具体的代码模式
    const code = result.code;
    console.log('\n📋 具体代码模式检查:');
    console.log('- 包含"设置包含图片的 elements 数组":', code.includes('设置包含图片的 elements 数组') ? '✅' : '❌');
    console.log('- 包含"为元素 1 加载图片":', code.includes('为元素 1 加载图片') ? '✅' : '❌');
    console.log('- 包含"设置元素 1 的source.bitmap":', code.includes('设置元素 1 的source.bitmap') ? '✅' : '❌');
    console.log('- 包含完整的source信息:', code.includes('"_url":"../projects/Project4/img/faces/Actor1.png"') ? '✅' : '❌');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testBackendAlignedBitmap();
}
