/**
 * 代码生成器工具函数
 */

import { OperationInfo, OperationType, OperationMode } from './types';

// ==================== 操作信息工具 ====================

/**
 * 检查是否为属性修改操作
 */
export function isPropertyModification(operation: OperationInfo): boolean {
  return operation.operationType === OperationType.MODIFY;
}

/**
 * 检查是否为对象创建操作
 */
export function isObjectCreation(operation: OperationInfo): boolean {
  return operation.operationType === OperationType.CREATE;
}

/**
 * 检查是否为对象删除操作
 */
export function isObjectDeletion(operation: OperationInfo): boolean {
  return operation.operationType === OperationType.DELETE;
}

/**
 * 检查是否为类型操作
 */
export function isTypeOperation(operation: OperationInfo): boolean {
  return operation.operationMode === OperationMode.TYPE ||
    (operation.targetObject && operation.targetObject._rpgEditorTypeCreated === true);
}

/**
 * 检查是否为对象操作
 */
export function isObjectOperation(operation: OperationInfo): boolean {
  return operation.operationMode === OperationMode.OBJECT && !isTypeOperation(operation);
}

// ==================== 路径处理工具 ====================

/**
 * 获取场景名称
 */
export function getSceneName(objectPath: string[]): string {
  return objectPath.length > 0 ? objectPath[0] : 'Unknown';
}

/**
 * 检查是否为有效的场景路径
 */
export function isValidScenePath(objectPath: string[]): boolean {
  return objectPath.length > 0 && objectPath[0].startsWith('Scene_');
}

/**
 * 生成路径字符串
 */
export function generatePathString(objectPath: string[]): string {
  return objectPath.map(p => `"${p}"`).join(', ');
}

/**
 * 规范化对象路径
 */
export function normalizePath(objectPath: string[]): string[] {
  return objectPath.filter(p => p && p.trim().length > 0);
}

// ==================== 对象类型工具 ====================

/**
 * 获取对象的真实类型名称
 */
export function getRealObjectType(obj: any): string {
  if (!obj) return 'Unknown';

  // 检查是否有自定义类型标记
  if (obj._rpgEditorObjectType) {
    return obj._rpgEditorObjectType;
  }

  // 使用构造函数名称
  if (obj.constructor && obj.constructor.name) {
    return obj.constructor.name;
  }

  return 'Unknown';
}

/**
 * 检查对象是否为Sprite类型
 */
export function isSprite(obj: any): boolean {
  return obj && (
    obj.constructor?.name === 'Sprite' ||
    obj instanceof (globalThis as any).PIXI?.Sprite ||
    obj._texture !== undefined
  );
}

/**
 * 检查对象是否为Container类型
 */
export function isContainer(obj: any): boolean {
  return obj && (
    obj.constructor?.name === 'Container' ||
    obj instanceof (globalThis as any).PIXI?.Container ||
    Array.isArray(obj.children)
  );
}

/**
 * 检查对象是否为Window类型
 */
export function isWindow(obj: any): boolean {
  return obj && obj.constructor?.name?.includes('Window');
}

// ==================== 属性处理工具 ====================

/**
 * 检查属性是否为基础属性
 */
export function isBasicProperty(propertyName: string): boolean {
  const basicProperties = [
    'x', 'y', 'width', 'height', 'visible', 'alpha', 'rotation', 'scale',
    'scaleX', 'scaleY', 'skewX', 'skewY', 'pivotX', 'pivotY', 'anchor'
  ];
  return basicProperties.includes(propertyName);
}

/**
 * 检查属性是否为Sprite特有属性
 */
export function isSpriteProperty(propertyName: string): boolean {
  const spriteProperties = [
    'texture', 'tint', 'blendMode', 'hue', 'colorTone', 'blendColor'
  ];
  return spriteProperties.includes(propertyName);
}

/**
 * 检查属性是否为Container特有属性
 */
export function isContainerProperty(propertyName: string): boolean {
  const containerProperties = [
    'children', 'sortableChildren', 'sortDirty'
  ];
  return containerProperties.includes(propertyName);
}

/**
 * 获取属性的类型分类
 */
export function getPropertyCategory(propertyName: string, objectType: string): string {
  if (isBasicProperty(propertyName)) return 'basic';
  if (isSpriteProperty(propertyName)) return 'sprite';
  if (isContainerProperty(propertyName)) return 'container';
  return 'custom';
}

// ==================== 代码格式化工具 ====================

/**
 * 清理代码字符串
 */
export function cleanCode(code: string): string {
  return code
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .join('\n');
}

/**
 * 合并代码片段
 */
export function mergeCodeSnippets(snippets: string[]): string {
  return snippets
    .filter(snippet => snippet && snippet.trim().length > 0)
    .join('\n\n');
}

/**
 * 添加代码块包装
 */
export function wrapInBlock(code: string, blockType: 'function' | 'if' | 'try' = 'if'): string {
  switch (blockType) {
    case 'function':
      return `function() {\n${code}\n}`;
    case 'if':
      return `if (true) {\n${code}\n}`;
    case 'try':
      return `try {\n${code}\n} catch (error) {\n    console.error("代码执行失败:", error);\n}`;
    default:
      return code;
  }
}

// ==================== 验证工具 ====================

/**
 * 验证操作信息的完整性
 */
export function validateOperationInfo(operation: OperationInfo): string[] {
  const errors: string[] = [];

  // 对于从文件加载的操作，targetObject可能为null，这是允许的
  // 因为代码生成时不需要实际的对象引用，只需要路径和类名信息
  if (!operation.targetObject && !isLoadedOperation(operation)) {
    errors.push('目标对象不能为空');
  }

  if (!operation.objectPath || operation.objectPath.length === 0) {
    errors.push('对象路径不能为空');
  }

  if (!operation.className) {
    errors.push('类名不能为空');
  }

  if (isObjectOperation(operation) && !isValidScenePath(operation.objectPath)) {
    errors.push('对象操作需要有效的场景路径');
  }

  return errors;
}

/**
 * 检查是否为从文件加载的操作
 * 从文件加载的操作targetObject为null，但有完整的路径和类名信息
 */
export function isLoadedOperation(operation: OperationInfo): boolean {
  return operation.targetObject === null &&
    operation.objectPath &&
    operation.objectPath.length > 0 &&
    operation.className &&
    operation.className.length > 0;
}

/**
 * 验证生成的代码语法
 */
export function validateGeneratedCode(code: string): boolean {
  try {
    // 简单的语法检查
    new Function(code);
    return true;
  } catch (error) {
    console.warn('生成的代码语法检查失败:', error);
    return false;
  }
}

// ==================== 调试工具 ====================

/**
 * 生成调试信息
 */
export function generateDebugInfo(operation: OperationInfo): string {
  return JSON.stringify({
    type: operation.operationType,
    mode: operation.operationMode,
    path: operation.objectPath,
    class: operation.className,
    timestamp: operation.timestamp
  }, null, 2);
}

/**
 * 创建调试日志函数
 */
export function createDebugLogger(prefix: string) {
  return (message: string, ...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${prefix}] ${message}`, ...args);
    }
  };
}
