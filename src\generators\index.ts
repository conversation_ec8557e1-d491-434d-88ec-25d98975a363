/**
 * 代码生成器模块主入口
 * 导出所有生成器和相关类型
 */

// ==================== 核心类型和基础设施 ====================
export * from './core/types';
export * from './core/utils';
export { BaseGenerator } from './core/BaseGenerator';

// ==================== 主代码生成器 ====================
export { CodeGenerator } from './CodeGenerator';

// ==================== 属性生成器 ====================
export {
  PropertyGenerator,
  BasicPropertyGenerator,
  SpritePropertyGenerator,
  ContainerPropertyGenerator,
  CustomPropertyGenerator,
  type PropertyGeneratorHandler
} from './property/PropertyGenerator';

// ==================== 对象生成器 ====================
export {
  ObjectGenerator,
  CreationGenerator,
  DeletionGenerator
} from './object/ObjectGenerator';

// ==================== 输出格式化器 ====================
export {
  PluginFormatter
} from './output/PluginFormatter';
export type { PluginFormatterConfig } from './output/PluginFormatter';

// ==================== 便捷工厂函数 ====================

import { CodeGenerator } from './CodeGenerator';
import { PluginFormatter } from './output/PluginFormatter';
import {
  OperationInfo,
  GeneratorConfig,
  PluginFormatterConfig
} from './core/types';

/**
 * 创建默认的代码生成器实例
 */
export function createCodeGenerator(config?: Partial<GeneratorConfig>): CodeGenerator {
  return new CodeGenerator(config);
}

/**
 * 创建默认的插件格式化器实例
 */
export function createPluginFormatter(config?: Partial<PluginFormatterConfig>): PluginFormatter {
  return new PluginFormatter(config);
}

/**
 * 一键生成插件代码的便捷函数
 */
export function generatePluginCode(
  operations: OperationInfo[],
  generatorConfig?: Partial<GeneratorConfig>,
  formatterConfig?: Partial<PluginFormatterConfig>
): string {
  const generator = createCodeGenerator(generatorConfig);
  const formatter = createPluginFormatter(formatterConfig);

  const generatedCode = generator.generateCode(operations);
  return formatter.formatAsPlugin(generatedCode);
}

/**
 * 生成代码预览的便捷函数
 */
export function generateCodePreview(
  operations: OperationInfo[],
  config?: Partial<GeneratorConfig>
): string {
  const generator = createCodeGenerator(config);
  return generator.generatePreview(operations);
}

// ==================== 操作信息构建器 ====================

import {
  PropertyModificationInfo,
  PropertyChange,
  ObjectCreationInfo,
  ObjectDeletionInfo,
  OperationType,
  OperationMode
} from './core/types';

/**
 * 属性修改操作构建器
 */
export class PropertyModificationBuilder {
  private operation: Partial<PropertyModificationInfo> = {
    operationType: OperationType.MODIFY,
    properties: new Map()
  };

  static create(): PropertyModificationBuilder {
    return new PropertyModificationBuilder();
  }

  target(object: any): this {
    this.operation.targetObject = object;
    this.operation.className = object?.constructor?.name || 'Unknown';
    return this;
  }

  path(objectPath: string[]): this {
    this.operation.objectPath = objectPath;
    return this;
  }

  property(name: string, oldValue: any, newValue: any, propertyType?: string): this {
    // 兼容性：设置旧格式字段
    this.operation.propertyName = name;
    this.operation.oldValue = oldValue;
    this.operation.newValue = newValue;
    this.operation.propertyType = propertyType;

    // 新格式：添加到properties Map
    if (!this.operation.properties) {
      this.operation.properties = new Map();
    }

    const propertyChange: PropertyChange = {
      propertyName: name,
      oldValue,
      newValue,
      propertyType,
      timestamp: Date.now()
    };

    this.operation.properties.set(name, propertyChange);
    return this;
  }

  mode(operationMode: OperationMode): this {
    this.operation.operationMode = operationMode;
    return this;
  }

  build(): PropertyModificationInfo {
    // 生成唯一ID
    this.operation.id = `prop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.operation.timestamp = Date.now();

    // 验证必需字段
    if (!this.operation.targetObject) throw new Error('目标对象不能为空');
    if (!this.operation.objectPath) throw new Error('对象路径不能为空');
    if (!this.operation.propertyName) throw new Error('属性名不能为空');
    if (!this.operation.className) throw new Error('类名不能为空');

    // 设置默认操作模式
    if (!this.operation.operationMode) {
      this.operation.operationMode = this.operation.targetObject._rpgEditorTypeCreated ?
        OperationMode.TYPE : OperationMode.OBJECT;
    }

    // 确保properties Map存在
    if (!this.operation.properties) {
      this.operation.properties = new Map();
    }

    return this.operation as PropertyModificationInfo;
  }
}

/**
 * 对象创建操作构建器
 */
export class ObjectCreationBuilder {
  private operation: Partial<ObjectCreationInfo> = {
    operationType: OperationType.CREATE
  };

  static create(): ObjectCreationBuilder {
    return new ObjectCreationBuilder();
  }

  target(object: any): this {
    this.operation.targetObject = object;
    this.operation.className = object?.constructor?.name || 'Unknown';
    return this;
  }

  path(objectPath: string[]): this {
    this.operation.objectPath = objectPath;
    return this;
  }

  objectType(type: string): this {
    this.operation.objectType = type;
    return this;
  }

  parent(parentPath: string[]): this {
    this.operation.parentPath = parentPath;
    return this;
  }

  properties(props: Record<string, any>): this {
    this.operation.initialProperties = props;
    return this;
  }

  name(objectName: string): this {
    this.operation.objectName = objectName;
    return this;
  }

  mode(operationMode: OperationMode): this {
    this.operation.operationMode = operationMode;
    return this;
  }

  build(): ObjectCreationInfo {
    // 生成唯一ID
    this.operation.id = `create_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.operation.timestamp = Date.now();

    // 验证必需字段
    if (!this.operation.targetObject) throw new Error('目标对象不能为空');
    if (!this.operation.objectPath) throw new Error('对象路径不能为空');
    if (!this.operation.objectType) throw new Error('对象类型不能为空');
    if (!this.operation.parentPath) throw new Error('父对象路径不能为空');
    if (!this.operation.className) throw new Error('类名不能为空');

    // 设置默认值
    if (!this.operation.initialProperties) this.operation.initialProperties = {};
    if (!this.operation.operationMode) {
      this.operation.operationMode = this.operation.targetObject._rpgEditorTypeCreated ?
        OperationMode.TYPE : OperationMode.OBJECT;
    }

    return this.operation as ObjectCreationInfo;
  }
}

/**
 * 对象删除操作构建器
 */
export class ObjectDeletionBuilder {
  private operation: Partial<ObjectDeletionInfo> = {
    operationType: OperationType.DELETE
  };

  static create(): ObjectDeletionBuilder {
    return new ObjectDeletionBuilder();
  }

  target(object: any): this {
    this.operation.targetObject = object;
    this.operation.className = object?.constructor?.name || 'Unknown';
    return this;
  }

  path(objectPath: string[]): this {
    this.operation.objectPath = objectPath;
    this.operation.targetPath = objectPath;
    return this;
  }

  mode(operationMode: OperationMode): this {
    this.operation.operationMode = operationMode;
    return this;
  }

  build(): ObjectDeletionInfo {
    // 生成唯一ID
    this.operation.id = `delete_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.operation.timestamp = Date.now();

    // 验证必需字段
    if (!this.operation.targetObject) throw new Error('目标对象不能为空');
    if (!this.operation.objectPath) throw new Error('对象路径不能为空');
    if (!this.operation.targetPath) throw new Error('目标路径不能为空');
    if (!this.operation.className) throw new Error('类名不能为空');

    // 设置默认操作模式
    if (!this.operation.operationMode) {
      this.operation.operationMode = this.operation.targetObject._rpgEditorTypeCreated ?
        OperationMode.TYPE : OperationMode.OBJECT;
    }

    return this.operation as ObjectDeletionInfo;
  }
}

export { OperationQueue } from './core/OperationQueue';
