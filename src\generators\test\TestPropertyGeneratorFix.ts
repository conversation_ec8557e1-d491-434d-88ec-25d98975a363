/**
 * 测试修复后的PropertyGenerator
 * 验证是否正确使用BitmapGenerator处理bitmap.elements
 */

import { PropertyGenerator } from '../property/PropertyGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建测试用的PropertyModificationInfo
function createTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();

  // 添加颜色属性
  properties.set('colorTone', {
    propertyName: 'colorTone',
    oldValue: [0, 0, 0, 0],
    newValue: [0, 95, 0, 0],
    propertyType: 'color',
    timestamp: Date.now()
  });

  // 添加基础属性
  properties.set('x', {
    propertyName: 'x',
    oldValue: 0,
    newValue: -8,
    propertyType: 'number',
    timestamp: Date.now()
  });

  return {
    id: 'test_operation_1',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    // 兼容性字段
    propertyName: 'colorTone',
    oldValue: [0, 0, 0, 0],
    newValue: [0, 95, 0, 0],
    propertyType: 'color'
  };
}

// 检查代码是否包含字符分离问题
function checkForCharacterSeparation(code: string): boolean {
  // 检查是否有连续的单字符行
  const lines = code.split('\n');
  let consecutiveSingleCharLines = 0;

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (trimmedLine.length === 1 && /[a-zA-Z_]/.test(trimmedLine)) {
      consecutiveSingleCharLines++;
      if (consecutiveSingleCharLines >= 5) {
        return true; // 发现字符分离问题
      }
    } else {
      consecutiveSingleCharLines = 0;
    }
  }

  return false;
}

// 运行测试
export function testPropertyGeneratorFix(): void {
  console.log('开始测试PropertyGenerator修复...');

  const generator = new PropertyGenerator();
  const testOperation = createTestOperation();

  try {
    const result = generator.generate(testOperation);

    console.log('生成的代码:');
    console.log('='.repeat(50));
    console.log(result.code);
    console.log('='.repeat(50));

    // 检查是否有字符分离问题
    const hasCharacterSeparation = checkForCharacterSeparation(result.code);

    console.log('测试结果:');
    console.log('- 代码长度:', result.code.length);
    console.log('- 代码行数:', result.code.split('\n').length);
    console.log('- 是否有字符分离问题:', hasCharacterSeparation ? '❌ 是' : '✅ 否');

    // 检查代码是否包含预期的内容
    const code = result.code;
    const hasObjectLookup = code.includes('findObjectByScenePath');
    const hasColorTone = code.includes('setColorTone');
    const hasXProperty = code.includes('.x = -8');
    const hasOnlyOneObjectLookup = (code.match(/findObjectByScenePath/g) || []).length === 1;

    console.log('功能验证:');
    console.log('- 包含对象查找:', hasObjectLookup ? '✅' : '❌');
    console.log('- 包含颜色设置:', hasColorTone ? '✅' : '❌');
    console.log('- 包含x属性设置:', hasXProperty ? '✅' : '❌');
    console.log('- 只有一次对象查找:', hasOnlyOneObjectLookup ? '✅' : '❌');

    // 检查代码格式
    const hasProperVariableDeclaration = code.includes('const target_sprite_Scene_Title_2Path = ["Scene_Title", "2"];');
    const hasProperFunctionCall = code.includes('const target_sprite_Scene_Title_2 = findObjectByScenePath(target_sprite_Scene_Title_2Path);');

    console.log('格式验证:');
    console.log('- 正确的变量声明:', hasProperVariableDeclaration ? '✅' : '❌');
    console.log('- 正确的函数调用:', hasProperFunctionCall ? '✅' : '❌');

    // 总体评估
    const allTestsPassed = !hasCharacterSeparation &&
      hasObjectLookup &&
      hasColorTone &&
      hasXProperty &&
      hasOnlyOneObjectLookup &&
      hasProperVariableDeclaration &&
      hasProperFunctionCall;

    console.log('\n总体结果:', allTestsPassed ? '✅ 所有测试通过！' : '❌ 部分测试失败！');

    if (!allTestsPassed) {
      console.log('\n详细代码分析:');
      const lines = result.code.split('\n');
      lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        if (trimmedLine.length === 1 && /[a-zA-Z_]/.test(trimmedLine)) {
          console.log(`第${index + 1}行可能有问题: "${line}"`);
        }
      });
    }

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testPropertyGeneratorFix();
}
