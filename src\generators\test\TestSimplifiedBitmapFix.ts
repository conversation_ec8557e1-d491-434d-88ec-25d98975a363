/**
 * 测试简化后的bitmap.elements处理
 * 验证直接在PropertyGenerator中处理是否正确
 */

import { PropertyGenerator } from '../property/PropertyGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建测试操作
function createSimplifiedTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  const elementsValue = [
    {
      "type": "text",
      "text": "Project4",
      "x": 20,
      "y": 156,
      "maxWidth": 776,
      "lineHeight": 48,
      "align": "center",
      "bounds": {
        "x": 20,
        "y": 156,
        "width": 776,
        "height": 48
      }
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/enemies/Actor1_3.png",
        "width": 224,
        "height": 228
      },
      "sx": 0,
      "sy": 0,
      "sw": 224,
      "sh": 228,
      "dx": 547,
      "dy": 44,
      "dw": 224,
      "dh": 228,
      "bounds": {
        "x": 296,
        "y": 198,
        "width": 224,
        "height": 228
      }
    }
  ];
  
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: Date.now()
  });

  // 添加一个普通属性来测试混合处理
  properties.set('x', {
    propertyName: 'x',
    oldValue: 0,
    newValue: 17,
    propertyType: 'number',
    timestamp: Date.now()
  });

  return {
    id: 'simplified_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
}

// 检查代码结构
function analyzeCodeStructure(code: string): {
  hasCorrectStructure: boolean;
  issues: string[];
  details: string[];
} {
  const lines = code.split('\n');
  const issues: string[] = [];
  const details: string[] = [];
  
  // 检查大括号匹配
  let braceCount = 0;
  lines.forEach((line, index) => {
    const openBraces = (line.match(/\{/g) || []).length;
    const closeBraces = (line.match(/\}/g) || []).length;
    braceCount += openBraces - closeBraces;
    
    if (braceCount < 0) {
      issues.push(`第${index + 1}行: 大括号不匹配`);
    }
  });
  
  if (braceCount !== 0) {
    issues.push(`总体大括号不匹配，差值: ${braceCount}`);
  }
  
  // 检查变量名
  const hasCorrectVariableName = code.includes('target_sprite_Scene_Title_2');
  const hasIncorrectVariableName = code.includes('target_sprite_temp') || code.includes('target_sprite_.');
  
  if (!hasCorrectVariableName) {
    issues.push('缺少正确的变量名');
  }
  if (hasIncorrectVariableName) {
    issues.push('存在错误的变量名');
  }
  
  // 检查elements处理
  const hasElementsProcessing = code.includes('_bitmap.elements');
  const hasImageLoading = code.includes('ImageManager.loadBitmapFromUrl');
  const hasAddLoadListener = code.includes('addLoadListener');
  const hasSourceSetting = code.includes('.source._url');
  
  if (!hasElementsProcessing) {
    issues.push('缺少elements处理');
  }
  if (!hasImageLoading) {
    issues.push('缺少图片加载');
  }
  if (!hasAddLoadListener) {
    issues.push('缺少addLoadListener');
  }
  if (!hasSourceSetting) {
    issues.push('缺少source设置');
  }
  
  // 检查是否正确分离了source
  const hasDirectSourceAssignment = code.includes('"source":{"_url"');
  if (hasDirectSourceAssignment) {
    issues.push('直接设置了包含source的数组');
  }
  
  // 检查普通属性处理
  const hasXProperty = code.includes('.x = 17');
  if (!hasXProperty) {
    issues.push('缺少x属性设置');
  }
  
  // 详细信息
  details.push(`大括号匹配: ${braceCount === 0 ? '✅' : '❌'}`);
  details.push(`正确变量名: ${hasCorrectVariableName ? '✅' : '❌'}`);
  details.push(`错误变量名: ${hasIncorrectVariableName ? '❌' : '✅'}`);
  details.push(`elements处理: ${hasElementsProcessing ? '✅' : '❌'}`);
  details.push(`图片加载: ${hasImageLoading ? '✅' : '❌'}`);
  details.push(`异步回调: ${hasAddLoadListener ? '✅' : '❌'}`);
  details.push(`source设置: ${hasSourceSetting ? '✅' : '❌'}`);
  details.push(`正确分离source: ${!hasDirectSourceAssignment ? '✅' : '❌'}`);
  details.push(`x属性设置: ${hasXProperty ? '✅' : '❌'}`);
  
  return {
    hasCorrectStructure: issues.length === 0,
    issues,
    details
  };
}

export function testSimplifiedBitmapFix(): void {
  console.log('🔧 测试简化后的bitmap.elements处理...');
  
  const generator = new PropertyGenerator();
  const testOperation = createSimplifiedTestOperation();
  
  try {
    const result = generator.generate(testOperation);
    
    console.log('生成的代码:');
    console.log('='.repeat(80));
    console.log(result.code);
    console.log('='.repeat(80));
    
    // 分析代码结构
    const analysis = analyzeCodeStructure(result.code);
    
    console.log('\n🔍 代码结构分析:');
    analysis.details.forEach(detail => {
      console.log(`- ${detail}`);
    });
    
    if (analysis.issues.length > 0) {
      console.log('\n❌ 发现的问题:');
      analysis.issues.forEach(issue => {
        console.log(`- ${issue}`);
      });
    }
    
    console.log('\n🎯 总体结果:', analysis.hasCorrectStructure ? '✅ 代码结构正确！' : '❌ 代码结构有问题');
    
    if (analysis.hasCorrectStructure) {
      console.log('\n🎉 恭喜！简化后的bitmap.elements处理完全正确！');
      console.log('✅ 大括号匹配');
      console.log('✅ 变量名正确');
      console.log('✅ 正确分离source');
      console.log('✅ 异步图片加载');
      console.log('✅ 普通属性处理');
    } else {
      console.log('\n🔧 需要进一步修复的问题:');
      analysis.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    // 检查预期的代码模式
    const code = result.code;
    console.log('\n📋 预期的代码模式检查:');
    console.log('- 包含"设置包含图片的 elements 数组":', code.includes('设置包含图片的 elements 数组') ? '✅' : '❌');
    console.log('- 包含"为元素 1 加载图片":', code.includes('为元素 1 加载图片') ? '✅' : '❌');
    console.log('- 包含"设置元素 1 的source":', code.includes('设置元素 1 的source') ? '✅' : '❌');
    console.log('- 包含正确的变量引用:', code.includes('target_sprite_Scene_Title_2._bitmap.elements') ? '✅' : '❌');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testSimplifiedBitmapFix();
}
