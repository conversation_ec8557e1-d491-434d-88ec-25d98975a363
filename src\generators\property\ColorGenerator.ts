/**
 * 颜色属性代码生成器
 * 处理hue、colorTone、blendColor等Sprite颜色相关属性
 * 参考后端plugin_generator.rs中的颜色处理逻辑
 */

import { PropertyModificationInfo, CodeSnippet, PropertyChange } from '../core/types';
import { PropertyGeneratorHandler } from './PropertyGenerator';

export class ColorGenerator implements PropertyGeneratorHandler {
  
  /**
   * 检查是否可以处理该属性
   */
  canHandle(propertyName: string, objectType: string): boolean {
    const colorProperties = ['hue', 'colorTone', 'blendColor', 'tint'];
    return colorProperties.includes(propertyName);
  }

  /**
   * 生成颜色相关代码
   */
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { objectPath, className, properties } = operation;
    const variableName = this.getObjectVariableName(objectPath, className);

    // 收集所有颜色相关属性
    const colorProperties = new Map<string, PropertyChange>();
    
    if (properties) {
      for (const [propertyName, propertyChange] of properties) {
        if (this.canHandle(propertyName, className)) {
          colorProperties.set(propertyName, propertyChange);
        }
      }
    }

    // 兼容性：处理单属性格式
    if (operation.propertyName && this.canHandle(operation.propertyName, className)) {
      const propertyChange: PropertyChange = {
        propertyName: operation.propertyName,
        oldValue: operation.oldValue,
        newValue: operation.newValue,
        propertyType: operation.propertyType,
        timestamp: operation.timestamp
      };
      colorProperties.set(operation.propertyName, propertyChange);
    }

    if (colorProperties.size === 0) {
      return {
        code: '',
        description: 'No color properties to process',
        dependencies: []
      };
    }

    // 生成完整的颜色处理代码
    return this.generateColorCode(variableName, objectPath, className, colorProperties);
  }

  /**
   * 生成颜色代码的主要逻辑
   */
  private generateColorCode(
    variableName: string, 
    objectPath: string[], 
    className: string,
    properties: Map<string, PropertyChange>
  ): CodeSnippet {
    const lines: string[] = [];
    const dependencies: string[] = ['findObjectByScenePath'];

    // 生成对象查找代码
    lines.push(...this.generateObjectLookup(objectPath, variableName));

    lines.push(`if (${variableName}) {`);

    // 处理每个颜色属性
    for (const [propertyName, propertyChange] of properties) {
      lines.push(...this.generatePropertyCode(variableName, propertyName, propertyChange));
    }

    lines.push('}');

    return {
      code: lines.join('\n'),
      description: `处理 ${className} 的 ${properties.size} 个颜色属性`,
      dependencies,
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName,
        propertyCount: properties.size,
        colorProperties: Array.from(properties.keys())
      }
    };
  }

  /**
   * 生成单个颜色属性的代码
   */
  private generatePropertyCode(variableName: string, propertyName: string, propertyChange: PropertyChange): string[] {
    const lines: string[] = [];
    const value = propertyChange.newValue;

    switch (propertyName) {
      case 'hue':
        lines.push(`    // 设置色相`);
        lines.push(`    if (${variableName}.setHue) {`);
        lines.push(`        ${variableName}.setHue(${value});`);
        lines.push(`    }`);
        lines.push(`    if (DEBUG) console.log('设置属性 hue =', ${value});`);
        break;

      case 'colorTone':
        const colorToneArray = Array.isArray(value) ? value : [0, 0, 0, 0];
        const colorToneStr = `[${colorToneArray.join(', ')}]`;
        lines.push(`    // 设置色调`);
        lines.push(`    if (${variableName}.setColorTone) {`);
        lines.push(`        ${variableName}.setColorTone(${colorToneStr});`);
        lines.push(`    }`);
        lines.push(`    if (DEBUG) console.log('设置属性 colorTone =', ${colorToneStr});`);
        break;

      case 'blendColor':
        const blendColorArray = Array.isArray(value) ? value : [0, 0, 0, 0];
        const blendColorStr = `[${blendColorArray.join(', ')}]`;
        lines.push(`    // 设置混合颜色`);
        lines.push(`    if (${variableName}.setBlendColor) {`);
        lines.push(`        ${variableName}.setBlendColor(${blendColorStr});`);
        lines.push(`    }`);
        lines.push(`    if (DEBUG) console.log('设置属性 blendColor =', ${blendColorStr});`);
        break;

      case 'tint':
        lines.push(`    // 设置色调（PIXI属性）`);
        lines.push(`    ${variableName}.tint = ${this.serializeValue(value)};`);
        lines.push(`    if (DEBUG) console.log('设置属性 tint =', ${this.serializeValue(value)});`);
        break;

      default:
        // 其他颜色相关属性的通用处理
        lines.push(`    // 设置颜色属性 ${propertyName}`);
        lines.push(`    ${variableName}.${propertyName} = ${this.serializeValue(value)};`);
        lines.push(`    if (DEBUG) console.log('设置属性 ${propertyName} =', ${this.serializeValue(value)});`);
        break;
    }

    return lines;
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookup(objectPath: string[], variableName: string): string[] {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ];
  }

  /**
   * 生成对象变量名
   */
  private getObjectVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 序列化值为JavaScript代码
   */
  private serializeValue(value: any): string {
    if (typeof value === 'string') {
      return `"${value.replace(/"/g, '\\"')}"`;
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value);
    } else if (Array.isArray(value)) {
      return `[${value.map(v => this.serializeValue(v)).join(', ')}]`;
    } else if (value === null || value === undefined) {
      return 'null';
    } else {
      return JSON.stringify(value);
    }
  }

  /**
   * 验证颜色值
   */
  private validateColorValue(propertyName: string, value: any): boolean {
    switch (propertyName) {
      case 'hue':
        return typeof value === 'number' && value >= 0 && value <= 360;
      
      case 'colorTone':
      case 'blendColor':
        return Array.isArray(value) && value.length === 4 && 
               value.every(v => typeof v === 'number' && v >= -255 && v <= 255);
      
      case 'tint':
        return typeof value === 'number' || typeof value === 'string';
      
      default:
        return true;
    }
  }

  /**
   * 获取颜色属性的默认值
   */
  private getDefaultValue(propertyName: string): any {
    switch (propertyName) {
      case 'hue':
        return 0;
      
      case 'colorTone':
      case 'blendColor':
        return [0, 0, 0, 0];
      
      case 'tint':
        return 0xFFFFFF;
      
      default:
        return null;
    }
  }

  /**
   * 格式化颜色值用于显示
   */
  private formatColorValue(propertyName: string, value: any): string {
    switch (propertyName) {
      case 'hue':
        return `${value}°`;
      
      case 'colorTone':
      case 'blendColor':
        if (Array.isArray(value)) {
          return `[${value.join(', ')}]`;
        }
        return String(value);
      
      case 'tint':
        if (typeof value === 'number') {
          return `0x${value.toString(16).toUpperCase().padStart(6, '0')}`;
        }
        return String(value);
      
      default:
        return String(value);
    }
  }
}
