/**
 * 统一的Bitmap处理功能测试
 * 验证SpriteEditorDialog和BitmapGenerator的统一处理
 */

import { BitmapElementsService, ElementChange } from '../services/bitmap/BitmapElementsService';
import { BitmapGenerator } from '../generators/property/BitmapGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../generators/core/types';

// 创建测试用的elements数据
function createTestElements() {
  return [
    {
      "type": "text",
      "text": "Project4",
      "x": 17,
      "y": 68,
      "maxWidth": 776,
      "lineHeight": 48,
      "align": "center",
      "bounds": {
        "x": 20,
        "y": 156,
        "width": 776,
        "height": 48
      },
      "outlineColor": "#824545"
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/enemies/Actor1_3.png",
        "width": 224,
        "height": 228
      },
      "sx": 0,
      "sy": 0,
      "sw": 224,
      "sh": 228,
      "dx": 288,
      "dy": 146,
      "dw": 224,
      "dh": 228,
      "bounds": {
        "x": 296,
        "y": 198,
        "width": 224,
        "height": 228
      }
    }
  ];
}

// 测试BitmapElementsService的功能
export function testBitmapElementsService(): void {
  console.log('🧪 测试BitmapElementsService功能...');
  
  const originalElements = createTestElements();
  const modifiedElements = [...originalElements];
  
  // 修改文本内容
  modifiedElements[0] = {
    ...modifiedElements[0],
    text: "Modified Project4",
    x: 20 // 稍微改变位置
  };
  
  // 添加新的文本元素
  modifiedElements.push({
    type: "text",
    text: "New Text",
    x: 100,
    y: 200,
    maxWidth: 200,
    lineHeight: 24,
    align: "left"
  });
  
  // 测试变化检测
  const changes = BitmapElementsService.detectElementsChanges(originalElements, modifiedElements);
  
  console.log('检测到的变化:', changes);
  console.log('- 变化数量:', changes.length);
  console.log('- 修改的元素:', changes.filter(c => c.type === 'modified').length);
  console.log('- 新增的元素:', changes.filter(c => c.type === 'added').length);
  console.log('- 删除的元素:', changes.filter(c => c.type === 'removed').length);
  
  // 测试清理序列化
  const cleanElements = BitmapElementsService.cleanElementsForSerialization(originalElements);
  console.log('清理后的elements:', cleanElements);
  
  // 测试图片检测
  const hasImages = BitmapElementsService.hasImageElements(originalElements);
  console.log('包含图片元素:', hasImages);
  
  // 测试元素分离
  const { textElements, imageElements, imageUrls, cleanElements: separated } = 
    BitmapElementsService.separateElements(originalElements);
  
  console.log('分离结果:');
  console.log('- 文本元素数量:', textElements.length);
  console.log('- 图片元素数量:', imageElements.length);
  console.log('- 图片URL数量:', imageUrls.length);
  console.log('- 清理后元素数量:', separated.length);
  
  // 测试验证
  const validation = BitmapElementsService.validateElements(originalElements);
  console.log('验证结果:', validation.isValid ? '✅ 有效' : '❌ 无效');
  if (!validation.isValid) {
    console.log('验证错误:', validation.errors);
  }
  
  console.log('✅ BitmapElementsService测试完成\n');
}

// 测试BitmapGenerator的统一处理
export function testBitmapGenerator(): void {
  console.log('🧪 测试BitmapGenerator统一处理...');
  
  const generator = new BitmapGenerator();
  const elementsValue = createTestElements();
  
  // 创建测试操作
  const properties = new Map<string, PropertyChange>();
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: Date.now()
  });
  
  const testOperation: PropertyModificationInfo = {
    id: 'unified_bitmap_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
  
  try {
    const result = generator.generate(testOperation, {});
    
    console.log('生成的代码:');
    console.log('='.repeat(60));
    console.log(result.code);
    console.log('='.repeat(60));
    
    // 验证代码内容
    const code = result.code;
    const hasElementsProcessing = code.includes('_bitmap.elements');
    const hasImageDetection = code.includes('包含图片元素') || code.includes('不包含图片source');
    const hasImageLoading = code.includes('ImageManager.loadBitmapFromUrl');
    const hasSourceSetting = code.includes('.source._url') && code.includes('.source.bitmap');
    const hasRedrawTrigger = code.includes('redrawAll') || code.includes('_baseTexture.update');
    
    console.log('验证结果:');
    console.log('- 包含elements处理:', hasElementsProcessing ? '✅' : '❌');
    console.log('- 检测图片元素:', hasImageDetection ? '✅' : '❌');
    console.log('- 包含图片加载:', hasImageLoading ? '✅' : '❌');
    console.log('- 设置source属性:', hasSourceSetting ? '✅' : '❌');
    console.log('- 触发重绘:', hasRedrawTrigger ? '✅' : '❌');
    
    const allTestsPassed = hasElementsProcessing && hasImageDetection && hasImageLoading && hasSourceSetting;
    console.log('总体结果:', allTestsPassed ? '✅ 通过' : '❌ 失败');
    
  } catch (error) {
    console.error('❌ BitmapGenerator测试失败:', error);
  }
  
  console.log('✅ BitmapGenerator测试完成\n');
}

// 测试统一性
export function testUnification(): void {
  console.log('🧪 测试统一性...');
  
  const elements = createTestElements();
  
  // 测试BitmapElementsService和BitmapGenerator的一致性
  const hasImages1 = BitmapElementsService.hasImageElements(elements);
  
  const generator = new BitmapGenerator();
  // @ts-ignore - 访问私有方法进行测试
  const hasImages2 = generator.checkElementsForImages(elements);
  
  console.log('图片检测一致性:', hasImages1 === hasImages2 ? '✅ 一致' : '❌ 不一致');
  console.log('- BitmapElementsService:', hasImages1);
  console.log('- BitmapGenerator:', hasImages2);
  
  // 测试元素分离的一致性
  const { cleanElements } = BitmapElementsService.separateElements(elements);
  const cleanElements2 = BitmapElementsService.cleanElementsForSerialization(elements);
  
  console.log('清理方法一致性:', cleanElements.length === cleanElements2.length ? '✅ 一致' : '❌ 不一致');
  console.log('- separateElements结果长度:', cleanElements.length);
  console.log('- cleanElementsForSerialization结果长度:', cleanElements2.length);
  
  console.log('✅ 统一性测试完成\n');
}

// 运行所有测试
export function runUnifiedBitmapTests(): void {
  console.log('🚀 开始统一的Bitmap处理功能测试');
  console.log('='.repeat(80));
  
  testBitmapElementsService();
  testBitmapGenerator();
  testUnification();
  
  console.log('='.repeat(80));
  console.log('🎯 所有统一Bitmap测试完成！');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  runUnifiedBitmapTests();
}
