/**
 * 测试变量名修复
 * 验证生成的代码中变量名是否正确
 */

import { PropertyGenerator } from '../property/PropertyGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建测试操作
function createVariableNameTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  const elementsValue = [
    {
      "type": "text",
      "text": "Test Text",
      "x": 10,
      "y": 10,
      "maxWidth": 200,
      "lineHeight": 24,
      "align": "left"
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/enemies/Actor1_6.png",
        "width": 178,
        "height": 302
      },
      "sx": 0,
      "sy": 0,
      "sw": 178,
      "sh": 302,
      "dx": 100,
      "dy": 100,
      "dw": 178,
      "dh": 302
    }
  ];
  
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: Date.now()
  });

  return {
    id: 'variable_name_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
}

export function testVariableNameFix(): void {
  console.log('🔧 测试变量名修复...');
  
  const generator = new PropertyGenerator();
  const testOperation = createVariableNameTestOperation();
  
  try {
    const result = generator.generate(testOperation);
    
    console.log('生成的代码:');
    console.log('='.repeat(80));
    console.log(result.code);
    console.log('='.repeat(80));
    
    // 验证变量名
    const code = result.code;
    const lines = code.split('\n');
    
    // 期望的正确变量名
    const expectedVariableName = 'target_sprite_Scene_Title_2';
    
    // 检查是否有正确的变量名
    const hasCorrectVariableName = code.includes(expectedVariableName);
    
    // 检查是否有错误的变量名
    const hasIncorrectVariableName = code.includes('target_sprite_.');
    const hasIncompleteVariableName = code.includes('target_sprite_temp');
    
    // 检查变量名的使用情况
    const correctVariableUsages = (code.match(new RegExp(expectedVariableName, 'g')) || []).length;
    const incorrectVariableUsages = (code.match(/target_sprite_\./g) || []).length;
    
    console.log('\n🔍 变量名验证结果:');
    console.log('- 期望的变量名:', expectedVariableName);
    console.log('- 包含正确变量名:', hasCorrectVariableName ? '✅' : '❌');
    console.log('- 包含错误变量名:', hasIncorrectVariableName ? '❌' : '✅');
    console.log('- 包含临时变量名:', hasIncompleteVariableName ? '❌' : '✅');
    console.log('- 正确变量名使用次数:', correctVariableUsages);
    console.log('- 错误变量名使用次数:', incorrectVariableUsages);
    
    // 详细分析每一行
    console.log('\n📋 逐行分析:');
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (trimmedLine.includes('target_sprite_')) {
        const lineNumber = index + 1;
        if (trimmedLine.includes(expectedVariableName)) {
          console.log(`第${lineNumber}行: ✅ ${trimmedLine}`);
        } else if (trimmedLine.includes('target_sprite_.')) {
          console.log(`第${lineNumber}行: ❌ ${trimmedLine}`);
        } else if (trimmedLine.includes('target_sprite_temp')) {
          console.log(`第${lineNumber}行: ⚠️ ${trimmedLine}`);
        } else {
          console.log(`第${lineNumber}行: ❓ ${trimmedLine}`);
        }
      }
    });
    
    // 总体评估
    const isVariableNameFixed = hasCorrectVariableName && 
                               !hasIncorrectVariableName && 
                               !hasIncompleteVariableName &&
                               correctVariableUsages > 0 &&
                               incorrectVariableUsages === 0;
    
    console.log('\n🎯 总体结果:', isVariableNameFixed ? '✅ 变量名修复成功！' : '❌ 变量名仍有问题');
    
    if (!isVariableNameFixed) {
      console.log('\n❌ 问题分析:');
      if (!hasCorrectVariableName) {
        console.log('- 没有找到正确的变量名');
      }
      if (hasIncorrectVariableName) {
        console.log('- 仍然存在错误的变量名 (target_sprite_.)');
      }
      if (hasIncompleteVariableName) {
        console.log('- 存在临时变量名 (target_sprite_temp)');
      }
      if (incorrectVariableUsages > 0) {
        console.log(`- 有 ${incorrectVariableUsages} 处错误的变量名使用`);
      }
      
      console.log('\n🔧 可能的解决方案:');
      console.log('1. 检查PropertyGenerator中的变量名替换逻辑');
      console.log('2. 确保BitmapGenerator生成的变量名被正确替换');
      console.log('3. 验证正则表达式替换是否正确工作');
    } else {
      console.log('\n🎉 变量名修复成功！所有变量名都正确生成。');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testVariableNameFix();
}
