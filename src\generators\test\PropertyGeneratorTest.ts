/**
 * PropertyGenerator测试
 * 用于验证修复后的代码生成是否正常
 */

import { PropertyGenerator } from '../property/PropertyGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建测试用的PropertyModificationInfo
function createTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  // 添加颜色属性
  properties.set('colorTone', {
    propertyName: 'colorTone',
    oldValue: [0, 0, 0, 0],
    newValue: [0, 95, 0, 0],
    propertyType: 'color',
    timestamp: Date.now()
  });
  
  // 添加基础属性
  properties.set('x', {
    propertyName: 'x',
    oldValue: 0,
    newValue: -8,
    propertyType: 'number',
    timestamp: Date.now()
  });

  return {
    id: 'test_operation_1',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null, // 测试时不需要实际对象
    properties,
    // 兼容性字段
    propertyName: 'colorTone',
    oldValue: [0, 0, 0, 0],
    newValue: [0, 95, 0, 0],
    propertyType: 'color'
  };
}

// 运行测试
export function testPropertyGenerator(): void {
  console.log('开始测试PropertyGenerator...');
  
  const generator = new PropertyGenerator();
  const testOperation = createTestOperation();
  
  try {
    const result = generator.generate(testOperation);
    
    console.log('生成的代码:');
    console.log('='.repeat(50));
    console.log(result.code);
    console.log('='.repeat(50));
    
    console.log('代码描述:', result.description);
    console.log('依赖项:', result.dependencies);
    console.log('元数据:', result.metadata);
    
    // 检查代码是否包含预期的内容
    const code = result.code;
    const hasObjectLookup = code.includes('findObjectByScenePath');
    const hasColorTone = code.includes('setColorTone');
    const hasXProperty = code.includes('.x = -8');
    const hasOnlyOneObjectLookup = (code.match(/findObjectByScenePath/g) || []).length === 1;
    
    console.log('验证结果:');
    console.log('- 包含对象查找:', hasObjectLookup);
    console.log('- 包含颜色设置:', hasColorTone);
    console.log('- 包含x属性设置:', hasXProperty);
    console.log('- 只有一次对象查找:', hasOnlyOneObjectLookup);
    
    if (hasObjectLookup && hasColorTone && hasXProperty && hasOnlyOneObjectLookup) {
      console.log('✅ 测试通过！代码生成正常');
    } else {
      console.log('❌ 测试失败！代码生成有问题');
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testPropertyGenerator();
}
