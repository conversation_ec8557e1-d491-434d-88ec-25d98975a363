/**
 * 属性修改代码生成器
 * 负责生成各种属性修改的JavaScript代码
 */

import { BaseGenerator } from '../core/BaseGenerator';
import {
  OperationInfo,
  PropertyModificationInfo,
  PropertyChange,
  CodeSnippet,
  OperationType,
  PropertyGeneratorConfig,
  PropertyNotSupportedError
} from '../core/types';
import {
  isPropertyModification,
  getRealObjectType,
  getPropertyCategory,
  validateOperationInfo
} from '../core/utils';
import { BitmapGenerator } from './BitmapGenerator';
import { ColorGenerator } from './ColorGenerator';
import { FilterGenerator } from './FilterGenerator';

/**
 * 属性生成器主类
 */
export class PropertyGenerator extends BaseGenerator {
  private propertyGenerators: Map<string, PropertyGeneratorHandler>;

  constructor(config: Partial<PropertyGeneratorConfig> = {}) {
    const propertyConfig: PropertyGeneratorConfig = {
      handleSpecialProperties: true,
      validateValues: true,
      debug: false,
      minify: false,
      addComments: true,
      variablePrefix: 'rpgEditor_',
      indentSize: 2,
      ...config
    };

    super(propertyConfig);

    this.propertyGenerators = new Map();
    this.initializePropertyGenerators();
  }

  /**
   * 检查是否可以处理该操作
   */
  canHandle(operation: OperationInfo): boolean {
    return isPropertyModification(operation);
  }

  /**
   * 生成属性修改代码
   * 新架构：每个OperationInfo包含该对象的所有属性修改，生成完整的代码
   */
  generate(operation: OperationInfo): CodeSnippet {
    if (!this.canHandle(operation)) {
      throw new Error('PropertyGenerator 只能处理属性修改操作');
    }

    const propertyOp = operation as PropertyModificationInfo;
    this.validateOperation(propertyOp);

    // 生成完整的属性修改代码
    return this.generateCompletePropertyCode(propertyOp);
  }

  /**
   * 生成完整的属性修改代码
   * 新架构：每个OperationInfo包含该对象的所有属性修改
   */
  private generateCompletePropertyCode(operation: PropertyModificationInfo): CodeSnippet {
    const { objectPath, className, properties } = operation;

    // 收集所有属性修改
    const allProperties = new Map<string, PropertyChange>();

    // 添加properties Map中的属性
    if (properties && properties.size > 0) {
      for (const [propertyName, propertyChange] of properties) {
        allProperties.set(propertyName, propertyChange);
      }
    }

    // 兼容性：添加单属性格式的属性
    if (operation.propertyName && !allProperties.has(operation.propertyName)) {
      const propertyChange: PropertyChange = {
        propertyName: operation.propertyName,
        oldValue: operation.oldValue,
        newValue: operation.newValue,
        propertyType: operation.propertyType,
        timestamp: operation.timestamp
      };
      allProperties.set(operation.propertyName, propertyChange);
    }

    // 生成统一的对象查找和属性设置代码
    return this.generateUnifiedPropertyCode(operation, allProperties);
  }

  /**
   * 生成统一的属性代码（避免重复的对象查找）
   */
  private generateUnifiedPropertyCode(operation: PropertyModificationInfo, allProperties: Map<string, PropertyChange>): CodeSnippet {
    const { objectPath, className } = operation;
    const variableName = this.getObjectVariableName(objectPath, className);

    const lines: string[] = [];
    const dependencies: string[] = ['findObjectByScenePath'];

    // 生成对象查找代码（只生成一次）
    const objectLookupLines = this.generateObjectLookup(objectPath, variableName);
    if (Array.isArray(objectLookupLines)) {
      lines.push(...objectLookupLines);
    } else {
      // 如果返回的是字符串，按行分割
      lines.push(...objectLookupLines.split('\n'));
    }

    lines.push(`if (${variableName}) {`);

    // 按类型分组处理属性，但在同一个if块内
    const processedProperties = new Set<string>();

    // 处理bitmap相关属性
    const bitmapProperties = this.getBitmapProperties(allProperties);
    if (bitmapProperties.size > 0) {
      lines.push(...this.generateBitmapPropertyLines(variableName, bitmapProperties));
      bitmapProperties.forEach((_, key) => processedProperties.add(key));
    }

    // 处理颜色相关属性
    const colorProperties = this.getColorProperties(allProperties);
    if (colorProperties.size > 0) {
      lines.push(...this.generateColorPropertyLines(variableName, colorProperties));
      colorProperties.forEach((_, key) => processedProperties.add(key));
    }

    // 处理滤镜相关属性
    const filterProperties = this.getFilterProperties(allProperties);
    if (filterProperties.size > 0) {
      lines.push(...this.generateFilterPropertyLines(variableName, filterProperties));
      filterProperties.forEach((_, key) => processedProperties.add(key));
    }

    // 处理剩余的基础属性
    const remainingProperties = this.getRemainingPropertiesFromProcessed(allProperties, processedProperties);
    if (remainingProperties.size > 0) {
      lines.push(...this.generateBasicPropertyLines(variableName, remainingProperties));
    }

    lines.push('}');

    return {
      code: lines.join('\n'),
      description: `设置 ${className} 的 ${allProperties.size} 个属性`,
      dependencies,
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName,
        propertyCount: allProperties.size,
        isComplete: true
      }
    };
  }

  /**
   * 生成单属性修改代码（兼容性方法）
   */
  private generateSinglePropertyCode(operation: PropertyModificationInfo): CodeSnippet {
    const objectType = getRealObjectType(operation.targetObject);
    const propertyCategory = getPropertyCategory(operation.propertyName!, objectType);

    // 获取对应的属性生成器
    const generator = this.getPropertyGenerator(propertyCategory, objectType);

    if (!generator) {
      throw new PropertyNotSupportedError(operation.propertyName!, objectType);
    }

    return generator.generate(operation, this.context);
  }

  /**
   * 检查是否有bitmap相关属性
   */
  private hasBitmapProperties(properties: Map<string, PropertyChange>): boolean {
    for (const propertyName of properties.keys()) {
      if (propertyName.startsWith('_bitmap.') ||
        propertyName === 'text' ||
        propertyName === 'texture' ||
        propertyName === 'bitmap._url') {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否有颜色相关属性
   */
  private hasColorProperties(properties: Map<string, PropertyChange>): boolean {
    const colorProperties = ['hue', 'colorTone', 'blendColor', 'tint'];
    for (const propertyName of properties.keys()) {
      if (colorProperties.includes(propertyName)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否有滤镜相关属性
   */
  private hasFilterProperties(properties: Map<string, PropertyChange>): boolean {
    for (const propertyName of properties.keys()) {
      if (propertyName.startsWith('filters') ||
        propertyName.includes('filters_operation') ||
        propertyName.includes('filter_param')) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取剩余的基础属性（排除已处理的特殊属性）
   */
  private getRemainingProperties(allProperties: Map<string, PropertyChange>): Map<string, PropertyChange> {
    const remaining = new Map<string, PropertyChange>();

    for (const [propertyName, propertyChange] of allProperties) {
      // 排除bitmap相关属性
      if (propertyName.startsWith('_bitmap.') ||
        propertyName === 'text' ||
        propertyName === 'texture' ||
        propertyName === 'bitmap._url') {
        continue;
      }

      // 排除颜色相关属性
      const colorProperties = ['hue', 'colorTone', 'blendColor', 'tint'];
      if (colorProperties.includes(propertyName)) {
        continue;
      }

      // 排除滤镜相关属性
      if (propertyName.startsWith('filters') ||
        propertyName.includes('filters_operation') ||
        propertyName.includes('filter_param')) {
        continue;
      }

      // 剩余的基础属性
      remaining.set(propertyName, propertyChange);
    }

    return remaining;
  }

  /**
   * 获取bitmap相关属性
   */
  private getBitmapProperties(allProperties: Map<string, PropertyChange>): Map<string, PropertyChange> {
    const bitmapProps = new Map<string, PropertyChange>();
    for (const [propertyName, propertyChange] of allProperties) {
      if (propertyName.startsWith('_bitmap.') ||
        propertyName === 'text' ||
        propertyName === 'texture' ||
        propertyName === 'bitmap._url') {
        bitmapProps.set(propertyName, propertyChange);
      }
    }
    return bitmapProps;
  }

  /**
   * 获取颜色相关属性
   */
  private getColorProperties(allProperties: Map<string, PropertyChange>): Map<string, PropertyChange> {
    const colorProps = new Map<string, PropertyChange>();
    const colorPropertyNames = ['hue', 'colorTone', 'blendColor', 'tint'];
    for (const [propertyName, propertyChange] of allProperties) {
      if (colorPropertyNames.includes(propertyName)) {
        colorProps.set(propertyName, propertyChange);
      }
    }
    return colorProps;
  }

  /**
   * 获取滤镜相关属性
   */
  private getFilterProperties(allProperties: Map<string, PropertyChange>): Map<string, PropertyChange> {
    const filterProps = new Map<string, PropertyChange>();
    for (const [propertyName, propertyChange] of allProperties) {
      if (propertyName.startsWith('filters') ||
        propertyName.includes('filters_operation') ||
        propertyName.includes('filter_param')) {
        filterProps.set(propertyName, propertyChange);
      }
    }
    return filterProps;
  }

  /**
   * 从已处理的属性集合中获取剩余属性
   */
  private getRemainingPropertiesFromProcessed(allProperties: Map<string, PropertyChange>, processedProperties: Set<string>): Map<string, PropertyChange> {
    const remaining = new Map<string, PropertyChange>();

    for (const [propertyName, propertyChange] of allProperties) {
      if (!processedProperties.has(propertyName)) {
        remaining.set(propertyName, propertyChange);
      }
    }

    return remaining;
  }

  /**
   * 生成bitmap属性行（不包含对象查找）
   * 使用BitmapGenerator处理复杂的bitmap逻辑
   */
  private generateBitmapPropertyLines(variableName: string, properties: Map<string, PropertyChange>): string[] {
    // 使用BitmapGenerator处理bitmap属性
    const bitmapGenerator = new BitmapGenerator();

    // 创建临时的PropertyModificationInfo来调用BitmapGenerator
    // 使用实际的objectPath和className来确保变量名正确
    const tempOperation: PropertyModificationInfo = {
      id: 'temp_bitmap_operation',
      operationType: OperationType.MODIFY,
      operationMode: 'object' as any,
      timestamp: Date.now(),
      objectPath: ['temp'], // 使用临时路径
      className: 'Sprite',
      targetObject: null,
      properties,
      propertyName: '',
      oldValue: null,
      newValue: null,
      propertyType: 'object'
    };

    try {
      const result = bitmapGenerator.generate(tempOperation, {});

      // 提取生成的代码，去掉对象查找部分，只保留属性设置部分
      const codeLines = result.code.split('\n');
      const propertyLines: string[] = [];

      let insideIfBlock = false;
      let braceCount = 0;

      // 获取BitmapGenerator生成的变量名，用于替换
      const bitmapVariableName = this.getObjectVariableName(['temp'], 'Sprite');

      for (const line of codeLines) {
        // 跳过对象查找代码
        if (line.includes('findObjectByScenePath') || line.includes('Path = [')) {
          continue;
        }

        // 检测if块开始
        if (line.includes('if (') && line.includes(') {')) {
          insideIfBlock = true;
          continue; // 跳过这一行，因为外层已经有if块了
        }

        if (insideIfBlock) {
          // 替换变量名并添加属性设置代码
          let processedLine = line;
          if (bitmapVariableName !== variableName) {
            processedLine = line.replace(new RegExp(bitmapVariableName, 'g'), variableName);
          }

          propertyLines.push(processedLine);

          // 计算大括号层级（在添加行之后）
          const openBraces = (line.match(/\{/g) || []).length;
          const closeBraces = (line.match(/\}/g) || []).length;
          braceCount += openBraces - closeBraces;

          // 如果大括号层级回到0或负数，说明if块结束了
          if (braceCount <= 0) {
            break;
          }
        }
      }

      return propertyLines;

    } catch (error) {
      console.error('使用BitmapGenerator处理bitmap属性时出错:', error);

      // 降级到简单处理
      return this.generateSimpleBitmapPropertyLines(variableName, properties);
    }
  }

  /**
   * 简单的bitmap属性处理（降级方案）
   */
  private generateSimpleBitmapPropertyLines(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];

    for (const [propertyName, propertyChange] of properties) {
      if (propertyName === 'texture' || propertyName === 'bitmap._url') {
        const imagePath = propertyChange.newValue as string;
        lines.push(`    // 更新 PIXI.js Sprite 纹理`);
        lines.push(`    const newBitmap = ImageManager.loadBitmapFromUrl("${imagePath}");`);
        lines.push(`    newBitmap.addLoadListener(function (bitmap) {`);
        lines.push(`        ${variableName}.bitmap = bitmap;`);
        lines.push(`    });`);
        lines.push(`    if (DEBUG) console.log('设置 Sprite 纹理 = ', "${imagePath}");`);
      } else if (propertyName === 'text') {
        const valueStr = this.serializeValue(propertyChange.newValue);
        lines.push(`    // 设置文本内容`);
        lines.push(`    ${variableName}.text = ${valueStr};`);
        lines.push(`    if (DEBUG) console.log('设置文本内容 =', ${valueStr});`);
      } else if (propertyName.startsWith('_bitmap.')) {
        const bitmapProp = propertyName.replace('_bitmap.', '');
        const valueStr = this.serializeValue(propertyChange.newValue);
        lines.push(`    // 设置 ${bitmapProp} 属性`);
        lines.push(`    if (${variableName}._bitmap) {`);
        lines.push(`        ${variableName}._bitmap.${bitmapProp} = ${valueStr};`);
        lines.push(`        if (DEBUG) console.log('设置文字样式 ${bitmapProp} =', ${valueStr});`);
        lines.push(`    }`);
      }
    }

    return lines;
  }

  /**
   * 生成颜色属性行（不包含对象查找）
   */
  private generateColorPropertyLines(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];

    for (const [propertyName, propertyChange] of properties) {
      const value = propertyChange.newValue;

      switch (propertyName) {
        case 'hue':
          lines.push(`    // 设置色相`);
          lines.push(`    if (${variableName}.setHue) {`);
          lines.push(`        ${variableName}.setHue(${value});`);
          lines.push(`    }`);
          lines.push(`    if (DEBUG) console.log('设置属性 hue =', ${value});`);
          break;

        case 'colorTone':
          const colorToneArray = Array.isArray(value) ? value : [0, 0, 0, 0];
          const colorToneStr = `[${colorToneArray.join(', ')}]`;
          lines.push(`    // 设置色调`);
          lines.push(`    if (${variableName}.setColorTone) {`);
          lines.push(`        ${variableName}.setColorTone(${colorToneStr});`);
          lines.push(`    }`);
          lines.push(`    if (DEBUG) console.log('设置属性 colorTone =', ${colorToneStr});`);
          break;

        case 'blendColor':
          const blendColorArray = Array.isArray(value) ? value : [0, 0, 0, 0];
          const blendColorStr = `[${blendColorArray.join(', ')}]`;
          lines.push(`    // 设置混合颜色`);
          lines.push(`    if (${variableName}.setBlendColor) {`);
          lines.push(`        ${variableName}.setBlendColor(${blendColorStr});`);
          lines.push(`    }`);
          lines.push(`    if (DEBUG) console.log('设置属性 blendColor =', ${blendColorStr});`);
          break;

        case 'tint':
          lines.push(`    // 设置色调（PIXI属性）`);
          lines.push(`    ${variableName}.tint = ${this.serializeValue(value)};`);
          lines.push(`    if (DEBUG) console.log('设置属性 tint =', ${this.serializeValue(value)});`);
          break;
      }
    }

    return lines;
  }

  /**
   * 生成滤镜属性行（不包含对象查找）
   */
  private generateFilterPropertyLines(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];

    // 简化实现，可以根据需要扩展
    for (const [propertyName, propertyChange] of properties) {
      lines.push(`    // 滤镜属性 ${propertyName} 处理待实现`);
      lines.push(`    if (DEBUG) console.log('滤镜属性:', '${propertyName}', ${this.serializeValue(propertyChange.newValue)});`);
    }

    return lines;
  }

  /**
   * 生成基础属性行（不包含对象查找）
   */
  private generateBasicPropertyLines(variableName: string, properties: Map<string, PropertyChange>): string[] {
    const lines: string[] = [];

    for (const [propertyName, propertyChange] of properties) {
      const propertyCode = `    ${variableName}.${propertyName} = ${this.serializeValue(propertyChange.newValue)};`;
      const debugCode = `    if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(propertyChange.newValue)}, ${variableName});`;

      lines.push(propertyCode);
      lines.push(debugCode);
    }

    return lines;
  }

  /**
   * 生成基础属性代码
   */
  private generateBasicPropertiesCode(operation: PropertyModificationInfo, properties: Map<string, PropertyChange>): CodeSnippet {
    const { objectPath, className } = operation;
    const variableName = this.getObjectVariableName(objectPath, className);

    const lines: string[] = [];
    const dependencies: string[] = ['findObjectByScenePath'];

    // 生成对象查找代码
    const objectLookupLines = this.generateObjectLookup(objectPath, variableName);
    if (Array.isArray(objectLookupLines)) {
      lines.push(...objectLookupLines);
    } else {
      // 如果返回的是字符串，按行分割
      lines.push(...objectLookupLines.split('\n'));
    }

    lines.push(`if (${variableName}) {`);

    // 生成所有基础属性的修改代码
    for (const [propertyName, propertyChange] of properties) {
      const propertyCode = `    ${variableName}.${propertyName} = ${this.serializeValue(propertyChange.newValue)};`;
      const debugCode = `    if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(propertyChange.newValue)}, ${variableName});`;

      lines.push(propertyCode);
      lines.push(debugCode);
    }

    lines.push('}');

    return {
      code: lines.join('\n'),
      description: `设置 ${className} 的 ${properties.size} 个基础属性`,
      dependencies,
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName,
        propertyCount: properties.size
      }
    };
  }

  /**
   * 生成对象变量名
   */
  private getObjectVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookup(objectPath: string[], variableName: string): string[] {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ];
  }

  /**
   * 序列化值为JavaScript代码
   */
  private serializeValue(value: any): string {
    if (typeof value === 'string') {
      return `"${value.replace(/"/g, '\\"')}"`;
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value);
    } else if (Array.isArray(value)) {
      return `[${value.map(v => this.serializeValue(v)).join(', ')}]`;
    } else if (value === null || value === undefined) {
      return 'null';
    } else {
      return JSON.stringify(value);
    }
  }

  /**
   * 初始化属性生成器
   */
  private initializePropertyGenerators(): void {
    // 基础属性生成器
    this.propertyGenerators.set('basic', new BasicPropertyGenerator());

    // Sprite属性生成器
    this.propertyGenerators.set('sprite', new SpritePropertyGenerator());

    // Container属性生成器
    this.propertyGenerators.set('container', new ContainerPropertyGenerator());

    // 自定义属性生成器
    this.propertyGenerators.set('custom', new CustomPropertyGenerator());

    // 专门的属性生成器
    this.propertyGenerators.set('bitmap', new BitmapGenerator());
    this.propertyGenerators.set('color', new ColorGenerator());
    this.propertyGenerators.set('filter', new FilterGenerator());
  }

  /**
   * 获取属性生成器
   */
  private getPropertyGenerator(category: string, objectType: string): PropertyGeneratorHandler | null {
    // 优先使用特定类型的生成器
    const specificGenerator = this.propertyGenerators.get(`${objectType.toLowerCase()}_${category}`);
    if (specificGenerator) {
      return specificGenerator;
    }

    // 使用通用类别生成器
    return this.propertyGenerators.get(category) || null;
  }

  /**
   * 注册自定义属性生成器
   */
  public registerPropertyGenerator(category: string, generator: PropertyGeneratorHandler): void {
    this.propertyGenerators.set(category, generator);
  }

  /**
   * 验证属性修改操作
   */
  protected validateOperation(operation: PropertyModificationInfo): void {
    // 调用父类的统一验证逻辑
    super.validateOperation(operation);

    // 属性修改特有的验证
    if (!operation.propertyName && (!operation.properties || operation.properties.size === 0)) {
      throw new Error('属性修改操作缺少属性信息');
    }

    // 检查配置中是否有validateValues属性
    const config = this.config as any;
    if (config.validateValues && operation.newValue === undefined) {
      throw new Error('属性值不能为undefined');
    }
  }
}

// ==================== 属性生成器接口 ====================

/**
 * 属性生成器处理接口
 */
export interface PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet;
  canHandle?(propertyName: string, objectType: string): boolean;
}

// ==================== 基础属性生成器 ====================

/**
 * 基础属性生成器
 * 处理 x, y, width, height, visible, alpha 等基础属性
 */
export class BasicPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { propertyName, newValue, objectPath, className } = operation;

    // 生成单个属性的设置代码（不包含对象查找）
    const propertyCode = `target.${propertyName} = ${this.serializeValue(newValue)};`;
    const debugCode = `if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(newValue)}, target);`;

    return {
      code: [propertyCode, debugCode].join('\n'),
      description: `设置 ${className} 的 ${propertyName} 属性为 ${newValue}`,
      dependencies: ['findObjectByScenePath'],
      // 添加元数据用于后续分组
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName: this.generateVariableName(objectPath, className)
      }
    };
  }

  private generateVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  private generateObjectLookup(objectPath: string[], variableName: string): string {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ].join('\n');
  }

  private generatePropertyAssignment(variableName: string, propertyName: string, value: any): string {
    const serializedValue = this.serializeValue(value);
    return [
      `if (${variableName}) {`,
      `    ${variableName}.${propertyName} = ${serializedValue};`,
      '}'
    ].join('\n');
  }

  private generateDebugLog(variableName: string, propertyName: string, value: any): string {
    return `if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(value)}, ${variableName});`;
  }

  private serializeValue(value: any): string {
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'number' || typeof value === 'boolean') return String(value);
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    return JSON.stringify(value);
  }
}

// ==================== Sprite属性生成器 ====================

/**
 * Sprite属性生成器
 * 处理 texture, tint, hue, colorTone 等Sprite特有属性
 */
export class SpritePropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { propertyName, newValue, objectPath, className } = operation;

    // 根据不同的Sprite属性生成不同的代码
    switch (propertyName) {
      case 'texture':
        return this.generateTextureCode(operation);
      case 'hue':
        return this.generateHueCode(operation);
      case 'colorTone':
        return this.generateColorToneCode(operation);
      case 'blendColor':
        return this.generateBlendColorCode(operation);
      default:
        // 使用基础属性生成器
        return new BasicPropertyGenerator().generate(operation, context);
    }
  }

  private generateTextureCode(operation: PropertyModificationInfo): CodeSnippet {
    // 纹理设置的特殊处理逻辑
    const { newValue, objectPath, className } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName}) {`,
      `    ${variableName}.texture = PIXI.Texture.from("${newValue}");`,
      `    if (DEBUG) console.log('设置纹理:', "${newValue}", ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite纹理为 ${newValue}`,
      dependencies: ['findObjectByScenePath', 'PIXI.Texture']
    };
  }

  private generateHueCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setHue) {`,
      `    ${variableName}.setHue(${newValue});`,
      `    if (DEBUG) console.log('设置色相:', ${newValue}, ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite色相为 ${newValue}`,
      dependencies: ['findObjectByScenePath']
    };
  }

  private generateColorToneCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;
    const colorArray = Array.isArray(newValue) ? newValue : [0, 0, 0, 0];

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setColorTone) {`,
      `    ${variableName}.setColorTone([${colorArray.join(', ')}]);`,
      `    if (DEBUG) console.log('设置色调:', [${colorArray.join(', ')}], ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite色调为 [${colorArray.join(', ')}]`,
      dependencies: ['findObjectByScenePath']
    };
  }

  private generateBlendColorCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;
    const colorArray = Array.isArray(newValue) ? newValue : [0, 0, 0, 0];

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setBlendColor) {`,
      `    ${variableName}.setBlendColor([${colorArray.join(', ')}]);`,
      `    if (DEBUG) console.log('设置混合颜色:', [${colorArray.join(', ')}], ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite混合颜色为 [${colorArray.join(', ')}]`,
      dependencies: ['findObjectByScenePath']
    };
  }
}

// ==================== Container属性生成器 ====================

/**
 * Container属性生成器
 * 处理Container特有的属性
 */
export class ContainerPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    // Container特有属性的处理逻辑
    return new BasicPropertyGenerator().generate(operation, context);
  }
}

// ==================== 自定义属性生成器 ====================

/**
 * 自定义属性生成器
 * 处理不在标准分类中的属性
 */
export class CustomPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    // 自定义属性的通用处理逻辑
    return new BasicPropertyGenerator().generate(operation, context);
  }
}
