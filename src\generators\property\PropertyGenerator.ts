/**
 * 属性修改代码生成器
 * 负责生成各种属性修改的JavaScript代码
 */

import { BaseGenerator } from '../core/BaseGenerator';
import {
  OperationInfo,
  PropertyModificationInfo,
  PropertyChange,
  CodeSnippet,
  OperationType,
  PropertyGeneratorConfig,
  PropertyNotSupportedError
} from '../core/types';
import {
  isPropertyModification,
  getRealObjectType,
  getPropertyCategory,
  validateOperationInfo
} from '../core/utils';
import { BitmapGenerator } from './BitmapGenerator';
import { ColorGenerator } from './ColorGenerator';
import { FilterGenerator } from './FilterGenerator';

/**
 * 属性生成器主类
 */
export class PropertyGenerator extends BaseGenerator {
  private propertyGenerators: Map<string, PropertyGeneratorHandler>;

  constructor(config: Partial<PropertyGeneratorConfig> = {}) {
    const propertyConfig: PropertyGeneratorConfig = {
      handleSpecialProperties: true,
      validateValues: true,
      debug: false,
      minify: false,
      addComments: true,
      variablePrefix: 'rpgEditor_',
      indentSize: 2,
      ...config
    };

    super(propertyConfig);

    this.propertyGenerators = new Map();
    this.initializePropertyGenerators();
  }

  /**
   * 检查是否可以处理该操作
   */
  canHandle(operation: OperationInfo): boolean {
    return isPropertyModification(operation);
  }

  /**
   * 生成属性修改代码
   * 新架构：每个OperationInfo包含该对象的所有属性修改，生成完整的代码
   */
  generate(operation: OperationInfo): CodeSnippet {
    if (!this.canHandle(operation)) {
      throw new Error('PropertyGenerator 只能处理属性修改操作');
    }

    const propertyOp = operation as PropertyModificationInfo;
    this.validateOperation(propertyOp);

    // 生成完整的属性修改代码
    return this.generateCompletePropertyCode(propertyOp);
  }

  /**
   * 生成完整的属性修改代码
   * 新架构：每个OperationInfo包含该对象的所有属性修改
   */
  private generateCompletePropertyCode(operation: PropertyModificationInfo): CodeSnippet {
    const { objectPath, className, properties } = operation;

    // 收集所有属性修改
    const allProperties = new Map<string, PropertyChange>();

    // 添加properties Map中的属性
    if (properties && properties.size > 0) {
      for (const [propertyName, propertyChange] of properties) {
        allProperties.set(propertyName, propertyChange);
      }
    }

    // 兼容性：添加单属性格式的属性
    if (operation.propertyName && !allProperties.has(operation.propertyName)) {
      const propertyChange: PropertyChange = {
        propertyName: operation.propertyName,
        oldValue: operation.oldValue,
        newValue: operation.newValue,
        propertyType: operation.propertyType,
        timestamp: operation.timestamp
      };
      allProperties.set(operation.propertyName, propertyChange);
    }

    // 按属性类型分组处理
    const codeSnippets: CodeSnippet[] = [];
    const allDependencies: string[] = [];

    // 处理bitmap相关属性
    const bitmapGenerator = this.propertyGenerators.get('bitmap') as BitmapGenerator;
    if (bitmapGenerator && this.hasBitmapProperties(allProperties)) {
      const bitmapSnippet = bitmapGenerator.generate(operation, this.context);
      if (bitmapSnippet.code.trim()) {
        codeSnippets.push(bitmapSnippet);
        allDependencies.push(...bitmapSnippet.dependencies);
      }
    }

    // 处理颜色相关属性
    const colorGenerator = this.propertyGenerators.get('color') as ColorGenerator;
    if (colorGenerator && this.hasColorProperties(allProperties)) {
      const colorSnippet = colorGenerator.generate(operation, this.context);
      if (colorSnippet.code.trim()) {
        codeSnippets.push(colorSnippet);
        allDependencies.push(...colorSnippet.dependencies);
      }
    }

    // 处理滤镜相关属性
    const filterGenerator = this.propertyGenerators.get('filter') as FilterGenerator;
    if (filterGenerator && this.hasFilterProperties(allProperties)) {
      const filterSnippet = filterGenerator.generate(operation, this.context);
      if (filterSnippet.code.trim()) {
        codeSnippets.push(filterSnippet);
        allDependencies.push(...filterSnippet.dependencies);
      }
    }

    // 处理剩余的基础属性
    const remainingProperties = this.getRemainingProperties(allProperties);
    if (remainingProperties.size > 0) {
      const basicSnippet = this.generateBasicPropertiesCode(operation, remainingProperties);
      if (basicSnippet.code.trim()) {
        codeSnippets.push(basicSnippet);
        allDependencies.push(...basicSnippet.dependencies);
      }
    }

    // 合并所有代码片段
    const combinedCode = codeSnippets.map(snippet => snippet.code).join('\n\n');
    const combinedDescription = codeSnippets.map(snippet => snippet.description).join('; ');

    return {
      code: combinedCode,
      description: combinedDescription || `设置 ${className} 的 ${allProperties.size} 个属性`,
      dependencies: [...new Set(allDependencies)], // 去重
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        propertyCount: allProperties.size,
        isComplete: true,
        snippetCount: codeSnippets.length
      }
    };
  }

  /**
   * 生成单属性修改代码（兼容性方法）
   */
  private generateSinglePropertyCode(operation: PropertyModificationInfo): CodeSnippet {
    const objectType = getRealObjectType(operation.targetObject);
    const propertyCategory = getPropertyCategory(operation.propertyName!, objectType);

    // 获取对应的属性生成器
    const generator = this.getPropertyGenerator(propertyCategory, objectType);

    if (!generator) {
      throw new PropertyNotSupportedError(operation.propertyName!, objectType);
    }

    return generator.generate(operation, this.context);
  }

  /**
   * 检查是否有bitmap相关属性
   */
  private hasBitmapProperties(properties: Map<string, PropertyChange>): boolean {
    for (const propertyName of properties.keys()) {
      if (propertyName.startsWith('_bitmap.') ||
        propertyName === 'text' ||
        propertyName === 'texture' ||
        propertyName === 'bitmap._url') {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否有颜色相关属性
   */
  private hasColorProperties(properties: Map<string, PropertyChange>): boolean {
    const colorProperties = ['hue', 'colorTone', 'blendColor', 'tint'];
    for (const propertyName of properties.keys()) {
      if (colorProperties.includes(propertyName)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否有滤镜相关属性
   */
  private hasFilterProperties(properties: Map<string, PropertyChange>): boolean {
    for (const propertyName of properties.keys()) {
      if (propertyName.startsWith('filters') ||
        propertyName.includes('filters_operation') ||
        propertyName.includes('filter_param')) {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取剩余的基础属性（排除已处理的特殊属性）
   */
  private getRemainingProperties(allProperties: Map<string, PropertyChange>): Map<string, PropertyChange> {
    const remaining = new Map<string, PropertyChange>();

    for (const [propertyName, propertyChange] of allProperties) {
      // 排除bitmap相关属性
      if (propertyName.startsWith('_bitmap.') ||
        propertyName === 'text' ||
        propertyName === 'texture' ||
        propertyName === 'bitmap._url') {
        continue;
      }

      // 排除颜色相关属性
      const colorProperties = ['hue', 'colorTone', 'blendColor', 'tint'];
      if (colorProperties.includes(propertyName)) {
        continue;
      }

      // 排除滤镜相关属性
      if (propertyName.startsWith('filters') ||
        propertyName.includes('filters_operation') ||
        propertyName.includes('filter_param')) {
        continue;
      }

      // 剩余的基础属性
      remaining.set(propertyName, propertyChange);
    }

    return remaining;
  }

  /**
   * 生成基础属性代码
   */
  private generateBasicPropertiesCode(operation: PropertyModificationInfo, properties: Map<string, PropertyChange>): CodeSnippet {
    const { objectPath, className } = operation;
    const variableName = this.getObjectVariableName(objectPath, className);

    const lines: string[] = [];
    const dependencies: string[] = ['findObjectByScenePath'];

    // 生成对象查找代码
    lines.push(...this.generateObjectLookup(objectPath, variableName));

    lines.push(`if (${variableName}) {`);

    // 生成所有基础属性的修改代码
    for (const [propertyName, propertyChange] of properties) {
      const propertyCode = `    ${variableName}.${propertyName} = ${this.serializeValue(propertyChange.newValue)};`;
      const debugCode = `    if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(propertyChange.newValue)}, ${variableName});`;

      lines.push(propertyCode);
      lines.push(debugCode);
    }

    lines.push('}');

    return {
      code: lines.join('\n'),
      description: `设置 ${className} 的 ${properties.size} 个基础属性`,
      dependencies,
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName,
        propertyCount: properties.size
      }
    };
  }

  /**
   * 生成对象变量名
   */
  private getObjectVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookup(objectPath: string[], variableName: string): string {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ].join('\n');
  }

  /**
   * 序列化值为JavaScript代码
   */
  private serializeValue(value: any): string {
    if (typeof value === 'string') {
      return `"${value.replace(/"/g, '\\"')}"`;
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value);
    } else if (Array.isArray(value)) {
      return `[${value.map(v => this.serializeValue(v)).join(', ')}]`;
    } else if (value === null || value === undefined) {
      return 'null';
    } else {
      return JSON.stringify(value);
    }
  }

  /**
   * 初始化属性生成器
   */
  private initializePropertyGenerators(): void {
    // 基础属性生成器
    this.propertyGenerators.set('basic', new BasicPropertyGenerator());

    // Sprite属性生成器
    this.propertyGenerators.set('sprite', new SpritePropertyGenerator());

    // Container属性生成器
    this.propertyGenerators.set('container', new ContainerPropertyGenerator());

    // 自定义属性生成器
    this.propertyGenerators.set('custom', new CustomPropertyGenerator());

    // 专门的属性生成器
    this.propertyGenerators.set('bitmap', new BitmapGenerator());
    this.propertyGenerators.set('color', new ColorGenerator());
    this.propertyGenerators.set('filter', new FilterGenerator());
  }

  /**
   * 获取属性生成器
   */
  private getPropertyGenerator(category: string, objectType: string): PropertyGeneratorHandler | null {
    // 优先使用特定类型的生成器
    const specificGenerator = this.propertyGenerators.get(`${objectType.toLowerCase()}_${category}`);
    if (specificGenerator) {
      return specificGenerator;
    }

    // 使用通用类别生成器
    return this.propertyGenerators.get(category) || null;
  }

  /**
   * 注册自定义属性生成器
   */
  public registerPropertyGenerator(category: string, generator: PropertyGeneratorHandler): void {
    this.propertyGenerators.set(category, generator);
  }

  /**
   * 验证属性修改操作
   */
  protected validateOperation(operation: PropertyModificationInfo): void {
    // 调用父类的统一验证逻辑
    super.validateOperation(operation);

    // 属性修改特有的验证
    if (!operation.propertyName && (!operation.properties || operation.properties.size === 0)) {
      throw new Error('属性修改操作缺少属性信息');
    }

    // 检查配置中是否有validateValues属性
    const config = this.config as any;
    if (config.validateValues && operation.newValue === undefined) {
      throw new Error('属性值不能为undefined');
    }
  }
}

// ==================== 属性生成器接口 ====================

/**
 * 属性生成器处理接口
 */
export interface PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet;
  canHandle?(propertyName: string, objectType: string): boolean;
}

// ==================== 基础属性生成器 ====================

/**
 * 基础属性生成器
 * 处理 x, y, width, height, visible, alpha 等基础属性
 */
export class BasicPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { propertyName, newValue, objectPath, className } = operation;

    // 生成单个属性的设置代码（不包含对象查找）
    const propertyCode = `target.${propertyName} = ${this.serializeValue(newValue)};`;
    const debugCode = `if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(newValue)}, target);`;

    return {
      code: [propertyCode, debugCode].join('\n'),
      description: `设置 ${className} 的 ${propertyName} 属性为 ${newValue}`,
      dependencies: ['findObjectByScenePath'],
      // 添加元数据用于后续分组
      metadata: {
        objectPath: objectPath.join('/'),
        className,
        variableName: this.generateVariableName(objectPath, className)
      }
    };
  }

  private generateVariableName(objectPath: string[], className: string): string {
    const pathStr = objectPath.join('_');
    return `target_${className.toLowerCase()}_${pathStr.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  private generateObjectLookup(objectPath: string[], variableName: string): string {
    const pathArray = objectPath.map(p => `"${p}"`).join(', ');
    return [
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ].join('\n');
  }

  private generatePropertyAssignment(variableName: string, propertyName: string, value: any): string {
    const serializedValue = this.serializeValue(value);
    return [
      `if (${variableName}) {`,
      `    ${variableName}.${propertyName} = ${serializedValue};`,
      '}'
    ].join('\n');
  }

  private generateDebugLog(variableName: string, propertyName: string, value: any): string {
    return `if (DEBUG) console.log('设置属性:', '${propertyName}', ${this.serializeValue(value)}, ${variableName});`;
  }

  private serializeValue(value: any): string {
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'number' || typeof value === 'boolean') return String(value);
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    return JSON.stringify(value);
  }
}

// ==================== Sprite属性生成器 ====================

/**
 * Sprite属性生成器
 * 处理 texture, tint, hue, colorTone 等Sprite特有属性
 */
export class SpritePropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    const { propertyName, newValue, objectPath, className } = operation;

    // 根据不同的Sprite属性生成不同的代码
    switch (propertyName) {
      case 'texture':
        return this.generateTextureCode(operation);
      case 'hue':
        return this.generateHueCode(operation);
      case 'colorTone':
        return this.generateColorToneCode(operation);
      case 'blendColor':
        return this.generateBlendColorCode(operation);
      default:
        // 使用基础属性生成器
        return new BasicPropertyGenerator().generate(operation, context);
    }
  }

  private generateTextureCode(operation: PropertyModificationInfo): CodeSnippet {
    // 纹理设置的特殊处理逻辑
    const { newValue, objectPath, className } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName}) {`,
      `    ${variableName}.texture = PIXI.Texture.from("${newValue}");`,
      `    if (DEBUG) console.log('设置纹理:', "${newValue}", ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite纹理为 ${newValue}`,
      dependencies: ['findObjectByScenePath', 'PIXI.Texture']
    };
  }

  private generateHueCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setHue) {`,
      `    ${variableName}.setHue(${newValue});`,
      `    if (DEBUG) console.log('设置色相:', ${newValue}, ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite色相为 ${newValue}`,
      dependencies: ['findObjectByScenePath']
    };
  }

  private generateColorToneCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;
    const colorArray = Array.isArray(newValue) ? newValue : [0, 0, 0, 0];

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setColorTone) {`,
      `    ${variableName}.setColorTone([${colorArray.join(', ')}]);`,
      `    if (DEBUG) console.log('设置色调:', [${colorArray.join(', ')}], ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite色调为 [${colorArray.join(', ')}]`,
      dependencies: ['findObjectByScenePath']
    };
  }

  private generateBlendColorCode(operation: PropertyModificationInfo): CodeSnippet {
    const { newValue, objectPath } = operation;
    const variableName = `sprite_${objectPath.join('_')}`;
    const colorArray = Array.isArray(newValue) ? newValue : [0, 0, 0, 0];

    const code = [
      `const ${variableName}Path = [${objectPath.map(p => `"${p}"`).join(', ')}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (${variableName} && ${variableName}.setBlendColor) {`,
      `    ${variableName}.setBlendColor([${colorArray.join(', ')}]);`,
      `    if (DEBUG) console.log('设置混合颜色:', [${colorArray.join(', ')}], ${variableName});`,
      '}'
    ].join('\n');

    return {
      code,
      description: `设置Sprite混合颜色为 [${colorArray.join(', ')}]`,
      dependencies: ['findObjectByScenePath']
    };
  }
}

// ==================== Container属性生成器 ====================

/**
 * Container属性生成器
 * 处理Container特有的属性
 */
export class ContainerPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    // Container特有属性的处理逻辑
    return new BasicPropertyGenerator().generate(operation, context);
  }
}

// ==================== 自定义属性生成器 ====================

/**
 * 自定义属性生成器
 * 处理不在标准分类中的属性
 */
export class CustomPropertyGenerator implements PropertyGeneratorHandler {
  generate(operation: PropertyModificationInfo, context: any): CodeSnippet {
    // 自定义属性的通用处理逻辑
    return new BasicPropertyGenerator().generate(operation, context);
  }
}
