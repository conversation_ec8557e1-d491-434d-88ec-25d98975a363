/**
 * 测试大括号修复
 * 验证生成的代码是否有正确的大括号匹配
 */

import { PropertyGenerator } from '../property/PropertyGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建测试操作
function createBracketTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  const elementsValue = [
    {
      "type": "text",
      "text": "Project5",
      "x": 17,
      "y": 104,
      "maxWidth": 776,
      "lineHeight": 48,
      "align": "center",
      "bounds": {
        "x": 20,
        "y": 156,
        "width": 776,
        "height": 48
      },
      "textColor": "#c56666",
      "fontSize": 71,
      "outlineWidth": 8.5
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/enemies/Actor1_6.png",
        "width": 178,
        "height": 302
      },
      "sx": 0,
      "sy": 0,
      "sw": 178,
      "sh": 302,
      "dx": 555,
      "dy": 8,
      "dw": 178,
      "dh": 302,
      "bounds": {
        "x": 319,
        "y": 161,
        "width": 178,
        "height": 302
      }
    }
  ];
  
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: Date.now()
  });

  return {
    id: 'bracket_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
}

// 检查大括号匹配
function checkBracketMatching(code: string): { isMatched: boolean; details: string[] } {
  const lines = code.split('\n');
  let braceCount = 0;
  let parenCount = 0;
  const details: string[] = [];
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    const trimmedLine = line.trim();
    
    // 计算大括号
    const openBraces = (line.match(/\{/g) || []).length;
    const closeBraces = (line.match(/\}/g) || []).length;
    braceCount += openBraces - closeBraces;
    
    // 计算圆括号
    const openParens = (line.match(/\(/g) || []).length;
    const closeParens = (line.match(/\)/g) || []).length;
    parenCount += openParens - closeParens;
    
    // 记录有大括号变化的行
    if (openBraces > 0 || closeBraces > 0) {
      details.push(`第${lineNumber}行: ${trimmedLine} (大括号: +${openBraces} -${closeBraces}, 累计: ${braceCount})`);
    }
    
    // 检查是否有不匹配的情况
    if (braceCount < 0) {
      details.push(`❌ 第${lineNumber}行: 大括号不匹配，多了右括号`);
    }
    if (parenCount < 0) {
      details.push(`❌ 第${lineNumber}行: 圆括号不匹配，多了右括号`);
    }
  });
  
  const isMatched = braceCount === 0 && parenCount === 0;
  
  if (!isMatched) {
    details.push(`❌ 最终结果: 大括号差值=${braceCount}, 圆括号差值=${parenCount}`);
  } else {
    details.push(`✅ 最终结果: 所有括号都匹配`);
  }
  
  return { isMatched, details };
}

export function testBracketFix(): void {
  console.log('🔧 测试大括号修复...');
  
  const generator = new PropertyGenerator();
  const testOperation = createBracketTestOperation();
  
  try {
    const result = generator.generate(testOperation);
    
    console.log('生成的代码:');
    console.log('='.repeat(80));
    console.log(result.code);
    console.log('='.repeat(80));
    
    // 检查大括号匹配
    const bracketCheck = checkBracketMatching(result.code);
    
    console.log('\n🔍 大括号匹配检查:');
    console.log('- 大括号匹配:', bracketCheck.isMatched ? '✅' : '❌');
    
    console.log('\n📋 详细分析:');
    bracketCheck.details.forEach(detail => {
      console.log(detail);
    });
    
    // 检查变量名
    const code = result.code;
    const expectedVariableName = 'target_sprite_Scene_Title_2';
    const hasCorrectVariableName = code.includes(expectedVariableName);
    const hasIncorrectVariableName = code.includes('target_sprite_temp') || code.includes('target_sprite_.');
    
    console.log('\n🔍 变量名检查:');
    console.log('- 正确的变量名:', hasCorrectVariableName ? '✅' : '❌');
    console.log('- 错误的变量名:', hasIncorrectVariableName ? '❌' : '✅');
    
    // 检查关键结构
    const hasAddLoadListener = code.includes('addLoadListener');
    const hasImagePath = code.includes('imagePath_1');
    const hasSourceSetting = code.includes('.source._url');
    
    console.log('\n🔍 结构检查:');
    console.log('- 包含addLoadListener:', hasAddLoadListener ? '✅' : '❌');
    console.log('- 包含imagePath变量:', hasImagePath ? '✅' : '❌');
    console.log('- 包含source设置:', hasSourceSetting ? '✅' : '❌');
    
    // 总体评估
    const allTestsPassed = bracketCheck.isMatched && 
                          hasCorrectVariableName && 
                          !hasIncorrectVariableName &&
                          hasAddLoadListener &&
                          hasImagePath &&
                          hasSourceSetting;
    
    console.log('\n🎯 总体结果:', allTestsPassed ? '✅ 所有测试通过！' : '❌ 部分测试失败');
    
    if (!allTestsPassed) {
      console.log('\n❌ 问题分析:');
      if (!bracketCheck.isMatched) {
        console.log('- 大括号不匹配');
      }
      if (!hasCorrectVariableName) {
        console.log('- 缺少正确的变量名');
      }
      if (hasIncorrectVariableName) {
        console.log('- 存在错误的变量名');
      }
      if (!hasAddLoadListener) {
        console.log('- 缺少addLoadListener');
      }
      
      console.log('\n🔧 建议的修复方案:');
      console.log('1. 检查PropertyGenerator的代码提取逻辑');
      console.log('2. 确保变量名替换正确工作');
      console.log('3. 验证大括号计数逻辑');
      console.log('4. 检查BitmapGenerator生成的原始代码');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testBracketFix();
}
