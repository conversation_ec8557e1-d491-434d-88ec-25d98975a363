/**
 * 前端代码生成服务
 * 整合代码生成器和后端API调用
 */

import { invoke } from '@tauri-apps/api/core';
import {
  CodeGenerator,
  PluginFormatter,
  PropertyModificationBuilder,
  ObjectCreationBuilder,
  ObjectDeletionBuilder,
  OperationInfo,
  GeneratorConfig,
  PluginFormatterConfig,
  OperationType,
  OperationMode,
  OperationQueue
} from '../../generators';
import { getObjectPath } from '../../utils/object/objectPro';
import useProjectStore from '../../store/Store';

/**
 * 代码生成服务配置
 */
export interface CodeGenerationServiceConfig {
  generator?: Partial<GeneratorConfig>;
  formatter?: Partial<PluginFormatterConfig>;
  batchSize?: number;
  autoSave?: boolean;
  debugMode?: boolean;
  saveToTempOnly?: boolean; // 新增：是否只保存到临时目录
}

/**
 * 代码生成服务主类
 */
export class CodeGenerationService {
  private codeGenerator: CodeGenerator;
  private pluginFormatter: PluginFormatter;
  private config: CodeGenerationServiceConfig;
  private operationQueue: OperationQueue;

  constructor(config: CodeGenerationServiceConfig = {}) {
    this.config = {
      batchSize: 10,
      autoSave: false, // 默认关闭自动保存
      debugMode: false,
      saveToTempOnly: true, // 默认只保存到临时目录
      ...config
    };

    this.codeGenerator = new CodeGenerator(this.config.generator);
    this.pluginFormatter = new PluginFormatter(this.config.formatter);
    this.operationQueue = OperationQueue.getInstance();

    if (this.config.debugMode) {
      console.log('[CodeGenerationService] 初始化完成', this.config);
    }
  }

  /**
   * 记录属性修改操作
   */
  public async recordPropertyModification(
    object: any,
    propertyName: string,
    oldValue: any,
    newValue: any
  ): Promise<string> {
    try {
      const operation = this.buildPropertyModification(object, propertyName, oldValue, newValue);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录属性修改:', operation);
      }

      this.operationQueue.addOperation(operation);

      return `属性 ${propertyName} 修改已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录属性修改失败:', error);
      throw error;
    }
  }

  /**
   * 记录对象创建操作
   * 注意：此方法应该在对象已经被创建并添加到父对象后调用
   */
  public async recordObjectCreation(
    parentObject: any,
    objectType: string,
    initialProperties: Record<string, any> = {},
    objectName?: string
  ): Promise<string> {
    try {
      // 查找刚创建的对象（通过名称或其他标识）
      const createdObject = this.findRecentlyCreatedObject(parentObject, objectName, objectType);

      // 获取创建对象的实际路径
      const actualPath = getObjectPath(createdObject);

      const operation = this.buildObjectCreationWithActualPath(parentObject, actualPath, objectType, initialProperties, objectName);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录对象创建:', operation);
        console.log('[CodeGenerationService] 实际路径:', actualPath);
      }

      this.operationQueue.addOperation(operation);

      return `对象创建操作已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录对象创建失败:', error);
      throw error;
    }
  }

  /**
   * 使用实际路径记录对象创建操作
   * 此方法直接接收实际路径，不需要查找对象
   */
  public async recordObjectCreationWithActualPath(
    parentObject: any,
    actualPath: string[],
    objectType: string,
    initialProperties: Record<string, any> = {},
    objectName?: string
  ): Promise<string> {
    try {
      const operation = this.buildObjectCreationWithActualPath(parentObject, actualPath, objectType, initialProperties, objectName);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录对象创建（实际路径）:', operation);
        console.log('[CodeGenerationService] 实际路径:', actualPath);
      }

      this.operationQueue.addOperation(operation);

      return `对象创建操作已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录对象创建失败:', error);
      throw error;
    }
  }

  /**
   * 记录对象删除操作
   */
  public async recordObjectDeletion(object: any): Promise<string> {
    try {
      const operation = this.buildObjectDeletion(object);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 记录对象删除:', operation);
      }

      this.operationQueue.addOperation(operation);

      return `对象删除操作已记录`;
    } catch (error) {
      console.error('[CodeGenerationService] 记录对象删除失败:', error);
      throw error;
    }
  }

  /**
   * 保存到临时目录（Ctrl+S 触发）
   */
  public async saveToTemp(): Promise<string> {
    const operations = this.operationQueue.getOperations();
    if (operations.length === 0) {
      return '没有待保存的操作';
    }

    try {
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 保存到临时目录，操作数量:', operations.length);
      }

      // 生成代码
      const generatedCode = this.codeGenerator.generateCode(operations);
      const pluginCode = this.pluginFormatter.formatAsPlugin(generatedCode);

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 生成的插件代码长度:', pluginCode.length);
      }

      // 调用后端API保存到临时目录
      const result = await invoke('save_to_temp_plugins', {
        request: {
          plugin_code: pluginCode,
          project_name: this.getCurrentProjectName()
        }
      });

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 临时保存完成:', result);
      }

      return result as string;
    } catch (error) {
      console.error('[CodeGenerationService] 临时保存失败:', error);
      throw error;
    }
  }

  /**
   * 刷新预览（复制到引擎和项目目录）
   */
  public async refreshPreview(): Promise<string> {
    try {
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 刷新预览');
      }

      // 调用后端API复制文件
      const result = await invoke('refresh_preview_from_temp', {
        project_name: this.getCurrentProjectName()
      });

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 预览刷新完成:', result);
      }

      return result as string;
    } catch (error) {
      console.error('[CodeGenerationService] 预览刷新失败:', error);
      throw error;
    }
  }

  /**
   * 立即保存所有操作（兼容性方法）
   */
  public async saveAllOperations(): Promise<string> {
    try {
      // 1. 先保存插件代码到临时目录
      const saveResult = await this.saveToTemp();

      // 2. 在代码生成之后保存操作记录到项目目录
      try {
        if (this.config.debugMode) {
          console.log('[CodeGenerationService] 保存操作记录到项目目录');
        }

        // 检查操作队列状态
        if (this.operationQueue.size() > 0) {
          await this.saveOperationsRecord();
          if (this.config.debugMode) {
            console.log('[CodeGenerationService] 操作记录保存完成');
          }
        } else {
          if (this.config.debugMode) {
            console.log('[CodeGenerationService] 操作队列为空，跳过操作记录保存');
          }
        }
      } catch (recordError) {
        console.error('[CodeGenerationService] 操作记录保存失败:', recordError);
        // 不阻止整个保存流程，只是记录错误
      }

      return saveResult;
    } catch (error) {
      console.error('[CodeGenerationService] 保存所有操作失败:', error);
      throw error;
    }
  }

  /**
   * 生成代码预览
   */
  public generatePreview(): string {
    if (this.operationQueue.size() === 0) {
      return '没有待生成的操作';
    }

    return this.codeGenerator.generatePreview(this.operationQueue.getOperations());
  }

  /**
   * 获取队列状态
   */
  public getQueueStatus(): { operationCount: number } {
    return {
      operationCount: this.operationQueue.size()
    };
  }

  /**
   * 保存操作记录到项目目录
   */
  public async saveOperationsRecord(): Promise<string> {
    try {
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 保存操作记录，数量:', this.operationQueue.size());
      }

      // 序列化操作队列，只保留必要的字段
      const serializableOperations = this.operationQueue.getOperations().map(operation => {
        // 创建基本操作信息
        const baseInfo = {
          id: operation.id,
          operationType: operation.operationType,
          operationMode: operation.operationMode,
          timestamp: operation.timestamp,
          objectPath: operation.objectPath,
          className: operation.className
        };

        // 根据操作类型添加特定字段
        switch (operation.operationType) {
          case 'MODIFY':
            const modifyOp = operation as any;
            const result: any = {
              ...baseInfo,
              // 兼容性字段，清理可能的PIXI对象引用
              propertyName: modifyOp.propertyName,
              oldValue: this.sanitizeValue(modifyOp.oldValue),
              newValue: this.sanitizeValue(modifyOp.newValue),
              propertyType: modifyOp.propertyType
            };

            // 新的多属性支持
            if (modifyOp.properties && modifyOp.properties.size > 0) {
              // 将Map转换为普通对象以便序列化，并清理PIXI对象引用
              const propertiesObj: Record<string, any> = {};
              for (const [key, value] of modifyOp.properties) {
                // 清理PropertyChange对象中的值
                const sanitizedValue = {
                  ...value,
                  oldValue: this.sanitizeValue(value.oldValue),
                  newValue: this.sanitizeValue(value.newValue)
                };
                propertiesObj[key] = sanitizedValue;
              }
              result.properties = propertiesObj;
            }

            return result;
          case 'CREATE':
            const createOp = operation as any;
            return {
              ...baseInfo,
              objectType: createOp.objectType,
              parentPath: createOp.parentPath,
              initialProperties: this.sanitizeProperties(createOp.initialProperties),
              objectName: createOp.objectName
            };
          case 'DELETE':
            return {
              ...baseInfo,
              targetPath: (operation as any).targetPath
            };
          default:
            return baseInfo;
        }
      });

      const operationsJson = JSON.stringify(serializableOperations, null, 2);

      // 调用后端API保存
      const result = await invoke('save_operations_record', {
        request: {
          operations: operationsJson,
          project_name: this.getCurrentProjectName()
        }
      });

      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 操作记录保存完成:', result);
      }

      return result as string;
    } catch (error) {
      console.error('[CodeGenerationService] 保存操作记录失败:', error);
      throw error;
    }
  }

  /**
   * 从项目目录加载操作记录
   */
  public async loadOperationsRecord(): Promise<boolean> {
    try {
      if (this.config.debugMode) {
        console.log('[CodeGenerationService] 加载操作记录');
      }

      // 调用后端API加载
      const response = await invoke('load_operations_record', {
        project_name: this.getCurrentProjectName()
      }) as { operations: string; found: boolean };

      if (response.found) {
        // 反序列化操作队列
        const loadedOperations = JSON.parse(response.operations);

        // 转换加载的操作为OperationInfo格式
        const restoredOperations = loadedOperations.map((operation: any) => {
          const baseOperation = {
            ...operation,
            targetObject: null // 设为 null，代码生成时不需要实际对象引用
          };

          // 如果是属性修改操作且有properties字段，转换为Map
          if (operation.operationType === 'MODIFY' && operation.properties) {
            const propertiesMap = new Map();
            for (const [key, value] of Object.entries(operation.properties)) {
              propertiesMap.set(key, value);
            }
            baseOperation.properties = propertiesMap;
          } else if (operation.operationType === 'MODIFY') {
            // 如果没有properties字段但有单属性字段，创建properties Map
            const propertiesMap = new Map();
            if (operation.propertyName) {
              const propertyChange = {
                propertyName: operation.propertyName,
                oldValue: operation.oldValue,
                newValue: operation.newValue,
                propertyType: operation.propertyType,
                timestamp: operation.timestamp
              };
              propertiesMap.set(operation.propertyName, propertyChange);
            }
            baseOperation.properties = propertiesMap;
          }

          return baseOperation;
        });

        // 将加载的操作与现有操作合并（而不是替换）
        const currentOperations = this.operationQueue.getOperations();
        this.operationQueue.clear();

        // 先添加现有操作，再添加加载的操作
        currentOperations.forEach(op => this.operationQueue.addOperation(op));
        restoredOperations.forEach((op: any) => this.operationQueue.addOperation(op));

        if (this.config.debugMode) {
          console.log('[CodeGenerationService] 操作记录加载完成，加载数量:', restoredOperations.length);
          console.log('[CodeGenerationService] 合并后总数量:', this.operationQueue.size());
        }

        return true;
      } else {
        if (this.config.debugMode) {
          console.log('[CodeGenerationService] 没有找到操作记录文件');
        }
        return false;
      }
    } catch (error) {
      console.error('[CodeGenerationService] 加载操作记录失败:', error);
      return false;
    }
  }

  /**
   * 清空操作队列
   */
  public clearQueue(): void {
    this.operationQueue.clear();
    this.codeGenerator.reset();

    if (this.config.debugMode) {
      console.log('[CodeGenerationService] 队列已清空');
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 构建属性修改操作
   */
  private buildPropertyModification(
    object: any,
    propertyName: string,
    oldValue: any,
    newValue: any
  ): OperationInfo {
    const operationMode = this.determineOperationMode(object);

    return PropertyModificationBuilder.create()
      .target(object)
      .path(getObjectPath(object))
      .property(propertyName, oldValue, newValue)
      .mode(operationMode)
      .build();
  }

  /**
   * 构建对象创建操作
   */
  private buildObjectCreation(
    parentObject: any,
    objectType: string,
    initialProperties: Record<string, any>,
    objectName?: string
  ): OperationInfo {
    const operationMode = this.determineOperationMode(parentObject);
    const parentPath = getObjectPath(parentObject);

    const builder = ObjectCreationBuilder.create()
      .target(parentObject)
      .path([...parentPath, 'new_child']) // 临时路径，实际路径会在创建后确定
      .objectType(objectType)
      .parent(parentPath)
      .properties(initialProperties)
      .mode(operationMode);

    // 只有当 objectName 存在时才设置名称
    if (objectName) {
      builder.name(objectName);
    }

    return builder.build();
  }

  /**
   * 使用实际路径构建对象创建操作
   */
  private buildObjectCreationWithActualPath(
    parentObject: any,
    actualPath: string[],
    objectType: string,
    initialProperties: Record<string, any>,
    objectName?: string
  ): OperationInfo {
    const operationMode = this.determineOperationMode(parentObject);
    const parentPath = getObjectPath(parentObject);

    const builder = ObjectCreationBuilder.create()
      .target(parentObject)
      .path(actualPath) // 使用实际路径
      .objectType(objectType)
      .parent(parentPath)
      .properties(initialProperties)
      .mode(operationMode);

    // 只有当 objectName 存在时才设置名称
    if (objectName) {
      builder.name(objectName);
    }

    return builder.build();
  }

  /**
   * 构建对象删除操作
   */
  private buildObjectDeletion(object: any): OperationInfo {
    const operationMode = this.determineOperationMode(object);

    return ObjectDeletionBuilder.create()
      .target(object)
      .path(getObjectPath(object))
      .mode(operationMode)
      .build();
  }

  /**
   * 确定操作模式
   */
  private determineOperationMode(object: any): OperationMode {
    // 检查对象是否有类型创建标记
    if (object._rpgEditorTypeCreated === true) {
      return OperationMode.TYPE;
    }

    // 检查当前选中对象状态
    const selectedObjects = useProjectStore.getState().selectedObjects;
    if (selectedObjects.className) {
      return OperationMode.TYPE;
    }

    return OperationMode.OBJECT;
  }

  /**
   * 获取当前项目名称
   */
  private getCurrentProjectName(): string | undefined {
    return useProjectStore.getState().projectName || undefined;
  }

  /**
   * 清理单个值，移除PIXI对象引用
   */
  private sanitizeValue(value: any): any {
    if (this.isSerializableValue(value)) {
      return value;
    }

    // 如果是PIXI对象，尝试提取基本属性
    if (value && typeof value === 'object') {
      const constructorName = value.constructor?.name || '';

      // 对于常见的PIXI对象，提取基本属性
      if (constructorName.includes('Sprite') || constructorName.includes('Container')) {
        return {
          x: value.x || 0,
          y: value.y || 0,
          visible: value.visible !== undefined ? value.visible : true,
          name: value.name || ''
        };
      }

      // 对于其他复杂对象，返回字符串表示
      return `[${constructorName}]`;
    }

    return null;
  }

  /**
   * 清理属性对象，移除PIXI对象引用，只保留可序列化的基本属性
   */
  private sanitizeProperties(properties: Record<string, any>): Record<string, any> {
    if (!properties || typeof properties !== 'object') {
      return {};
    }

    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(properties)) {
      // 清理每个属性值
      sanitized[key] = this.sanitizeValue(value);
    }

    return sanitized;
  }

  /**
   * 检查值是否可序列化
   */
  private isSerializableValue(value: any): boolean {
    if (value === null || value === undefined) {
      return true;
    }

    const type = typeof value;
    if (type === 'string' || type === 'number' || type === 'boolean') {
      return true;
    }

    if (Array.isArray(value)) {
      // 检查数组中的所有元素是否都可序列化
      return value.every(item => this.isSerializableValue(item));
    }

    if (type === 'object') {
      // 排除PIXI对象和其他复杂对象
      if (value.constructor && value.constructor.name) {
        const constructorName = value.constructor.name;
        // 排除PIXI相关的对象
        if (constructorName.startsWith('PIXI') ||
          constructorName.includes('Sprite') ||
          constructorName.includes('Container') ||
          constructorName.includes('Graphics') ||
          constructorName.includes('Transform') ||
          constructorName.includes('ObservablePoint') ||
          constructorName.includes('Bitmap') ||
          constructorName.includes('Texture')) {
          return false;
        }
      }

      // 对于普通对象，递归检查其属性
      try {
        for (const [, val] of Object.entries(value)) {
          if (!this.isSerializableValue(val)) {
            return false;
          }
        }
        return true;
      } catch (error) {
        // 如果遍历时出错（比如循环引用），则不可序列化
        return false;
      }
    }

    return false;
  }
}

// ==================== 单例实例 ====================

let codeGenerationServiceInstance: CodeGenerationService | null = null;

/**
 * 获取代码生成服务单例实例
 */
export function getCodeGenerationService(config?: CodeGenerationServiceConfig): CodeGenerationService {
  if (!codeGenerationServiceInstance) {
    codeGenerationServiceInstance = new CodeGenerationService(config);
    console.log('[CodeGenerationService] 创建新的单例实例，操作队列长度:', codeGenerationServiceInstance.getQueueStatus().operationCount);
  } else {
    // 如果实例已存在，直接返回现有实例
    console.log('[CodeGenerationService] 使用现有实例，操作队列长度:', codeGenerationServiceInstance.getQueueStatus().operationCount);
  }
  return codeGenerationServiceInstance;
}

/**
 * 重置代码生成服务实例
 */
export function resetCodeGenerationService(): void {
  if (codeGenerationServiceInstance) {
    codeGenerationServiceInstance.clearQueue();
    codeGenerationServiceInstance = null;
  }
}
