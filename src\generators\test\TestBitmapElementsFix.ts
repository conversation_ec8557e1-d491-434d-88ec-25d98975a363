/**
 * 测试修复后的bitmap elements处理
 * 验证包含图片的elements数组是否正确生成source加载代码
 */

import { BitmapGenerator } from '../property/BitmapGenerator';
import { PropertyModificationInfo, OperationType, OperationMode, PropertyChange } from '../core/types';

// 创建包含图片的elements测试数据
function createElementsWithImageTestOperation(): PropertyModificationInfo {
  const properties = new Map<string, PropertyChange>();
  
  // 使用您提供的真实数据
  const elementsValue = [
    {
      "type": "text",
      "text": "Project4",
      "x": 17,
      "y": 68,
      "maxWidth": 776,
      "lineHeight": 48,
      "align": "center",
      "bounds": {
        "x": 20,
        "y": 156,
        "width": 776,
        "height": 48
      },
      "outlineColor": "#824545"
    },
    {
      "type": "image",
      "source": {
        "_url": "../projects/Project4/img/enemies/Actor1_3.png",
        "width": 224,
        "height": 228
      },
      "sx": 0,
      "sy": 0,
      "sw": 224,
      "sh": 228,
      "dx": 288,
      "dy": 146,
      "dw": 224,
      "dh": 228,
      "bounds": {
        "x": 296,
        "y": 198,
        "width": 224,
        "height": 228
      }
    }
  ];
  
  properties.set('_bitmap.elements', {
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array',
    timestamp: 1748398226810
  });

  return {
    id: 'elements_with_image_test',
    operationType: OperationType.MODIFY,
    operationMode: OperationMode.OBJECT,
    timestamp: Date.now(),
    objectPath: ['Scene_Title', '2'],
    className: 'Sprite',
    targetObject: null,
    properties,
    propertyName: '_bitmap.elements',
    oldValue: [],
    newValue: elementsValue,
    propertyType: 'array'
  };
}

// 测试bitmap elements处理
export function testBitmapElementsWithImages(): void {
  console.log('🧪 开始测试包含图片的bitmap elements处理...');
  
  const generator = new BitmapGenerator();
  const testOperation = createElementsWithImageTestOperation();
  
  try {
    const result = generator.generate(testOperation, {});
    
    console.log('生成的代码:');
    console.log('='.repeat(80));
    console.log(result.code);
    console.log('='.repeat(80));
    
    // 验证代码内容
    const code = result.code;
    
    // 检查是否正确处理了包含图片的elements
    const hasElementsProcessing = code.includes('_bitmap.elements');
    const hasImageDetection = code.includes('包含图片元素') || code.includes('不包含图片source');
    const hasImageLoading = code.includes('ImageManager.loadBitmapFromUrl');
    const hasSourceSetting = code.includes('.source._url') && code.includes('.source.bitmap');
    const hasSizeSettings = code.includes('.source.width') && code.includes('.source.height');
    const hasRedrawTrigger = code.includes('redrawAll') || code.includes('_baseTexture.update');
    const hasAsyncCallback = code.includes('addLoadListener');
    
    // 检查是否正确分离了source
    const hasNoDirectSourceAssignment = !code.includes('source":{"_url"');
    
    console.log('\n验证结果:');
    console.log('- 包含elements处理:', hasElementsProcessing ? '✅' : '❌');
    console.log('- 检测到图片元素:', hasImageDetection ? '✅' : '❌');
    console.log('- 包含图片加载:', hasImageLoading ? '✅' : '❌');
    console.log('- 设置source属性:', hasSourceSetting ? '✅' : '❌');
    console.log('- 设置图片尺寸:', hasSizeSettings ? '✅' : '❌');
    console.log('- 触发重绘:', hasRedrawTrigger ? '✅' : '❌');
    console.log('- 异步加载回调:', hasAsyncCallback ? '✅' : '❌');
    console.log('- 正确分离source:', hasNoDirectSourceAssignment ? '✅' : '❌');
    
    // 检查生成的代码结构
    const lines = code.split('\n');
    const hasProperStructure = lines.some(line => 
      line.includes('设置不包含图片source的elements数组')
    );
    const hasImageLoadingLoop = lines.some(line => 
      line.includes('为元素 1 加载图片')
    );
    
    console.log('- 正确的代码结构:', hasProperStructure ? '✅' : '❌');
    console.log('- 图片加载循环:', hasImageLoadingLoop ? '✅' : '❌');
    
    // 总体评估
    const allTestsPassed = hasElementsProcessing && 
                          hasImageDetection && 
                          hasImageLoading && 
                          hasSourceSetting && 
                          hasSizeSettings && 
                          hasRedrawTrigger && 
                          hasAsyncCallback && 
                          hasNoDirectSourceAssignment &&
                          hasProperStructure &&
                          hasImageLoadingLoop;
    
    console.log('\n🎯 总体测试结果:', allTestsPassed ? '✅ 全部通过！' : '❌ 部分失败！');
    
    if (!allTestsPassed) {
      console.log('\n📋 详细分析:');
      console.log('预期的代码应该包含:');
      console.log('1. 设置不包含source的elements数组');
      console.log('2. 为图片元素异步加载bitmap');
      console.log('3. 在加载完成后设置source属性');
      console.log('4. 触发bitmap重绘');
      
      console.log('\n当前生成的代码问题:');
      if (!hasImageDetection) console.log('- 没有正确检测图片元素');
      if (!hasSourceSetting) console.log('- 没有设置source属性');
      if (!hasAsyncCallback) console.log('- 没有异步加载回调');
      if (!hasNoDirectSourceAssignment) console.log('- 直接设置了包含source的数组');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testBitmapElementsWithImages();
}
