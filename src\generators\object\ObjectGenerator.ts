/**
 * 对象生命周期代码生成器
 * 负责生成对象创建和删除的JavaScript代码
 */

import { BaseGenerator } from '../core/BaseGenerator';
import {
  OperationInfo,
  ObjectCreationInfo,
  ObjectDeletionInfo,
  CodeSnippet,
  OperationType,
  ObjectGeneratorConfig,
  ObjectTypeNotSupportedError
} from '../core/types';
import {
  isObjectCreation,
  isObjectDeletion,
  getRealObjectType,
  isTypeOperation,
  validateOperationInfo
} from '../core/utils';

/**
 * 对象生成器主类
 */
export class ObjectGenerator extends BaseGenerator {
  private creationGenerator: CreationGenerator;
  private deletionGenerator: DeletionGenerator;

  constructor(config: Partial<ObjectGeneratorConfig> = {}) {
    super({
      addTypeMarkers: true,
      generateUniqueNames: true,
      ...config
    });

    const objectConfig: ObjectGeneratorConfig = {
      addTypeMarkers: true,
      generateUniqueNames: true,
      debug: false,
      minify: false,
      addComments: true,
      variablePrefix: 'rpgEditor_',
      indentSize: 2,
      ...config
    };

    this.creationGenerator = new CreationGenerator(objectConfig);
    this.deletionGenerator = new DeletionGenerator(objectConfig);
  }

  /**
   * 检查是否可以处理该操作
   */
  canHandle(operation: OperationInfo): boolean {
    return isObjectCreation(operation) || isObjectDeletion(operation);
  }

  /**
   * 生成对象生命周期代码
   */
  generate(operation: OperationInfo): CodeSnippet {
    if (!this.canHandle(operation)) {
      throw new Error('ObjectGenerator 只能处理对象创建和删除操作');
    }

    this.validateOperation(operation);

    if (isObjectCreation(operation)) {
      return this.creationGenerator.generate(operation as ObjectCreationInfo, this.context);
    } else if (isObjectDeletion(operation)) {
      return this.deletionGenerator.generate(operation as ObjectDeletionInfo, this.context);
    }

    throw new Error('未知的对象操作类型');
  }
}

// ==================== 对象创建生成器 ====================

/**
 * 对象创建代码生成器
 */
export class CreationGenerator {
  private config: ObjectGeneratorConfig;
  private supportedTypes: Set<string>;

  constructor(config: ObjectGeneratorConfig) {
    this.config = config;
    this.supportedTypes = new Set([
      'Sprite', 'Container', 'Graphics', 'Text', 'BitmapText',
      'Window', 'Window_Base', 'Window_Selectable', 'Window_Command',
      'Label', 'Button', 'LayoutContainer'
    ]);
  }

  /**
   * 生成对象创建代码
   */
  generate(operation: ObjectCreationInfo, context: any): CodeSnippet {
    const { objectType, parentPath, objectPath, initialProperties, objectName } = operation;

    if (!this.supportedTypes.has(objectType)) {
      throw new ObjectTypeNotSupportedError(objectType);
    }

    // 从实际路径中提取目标索引
    const targetIndex = objectPath[objectPath.length - 1];

    const variableName = this.generateObjectVariableName(operation, context);
    const parentVariableName = this.generateParentVariableName(parentPath);

    const code = [
      this.generateParentLookupWithIndex(parentPath, parentVariableName, targetIndex),
      this.generateObjectCreation(objectType, variableName, initialProperties, objectName),
      this.generateTypeMarkers(operation, variableName),
      this.generateParentAttachment(parentVariableName, variableName),
      this.generateDebugLog(objectType, objectName || 'unnamed', variableName)
    ].filter(Boolean).join('\n\n');

    return {
      code,
      description: `创建 ${objectType} 对象${objectName ? ` (${objectName})` : ''}`,
      dependencies: ['findObjectByScenePath', 'createGameObject']
    };
  }

  /**
   * 生成对象变量名
   */
  private generateObjectVariableName(operation: ObjectCreationInfo, context: any): string {
    const { objectType, objectName } = operation;
    const timestamp = Date.now() % 10000;
    const baseName = objectName || objectType.toLowerCase();
    return `child_${baseName}_${timestamp}`;
  }

  /**
   * 生成父对象变量名
   */
  private generateParentVariableName(parentPath: string[]): string {
    return `parent_${parentPath.join('_').replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成父对象查找代码（带索引）
   * 使用创建操作标记，跳过最后的索引查找
   */
  private generateParentLookupWithIndex(parentPath: string[], variableName: string, targetIndex: string): string {
    // 为创建操作生成带有+标记的路径
    // 格式：["+", "Scene_Title", "实际索引"]
    const pathArray = ['"+"', ...parentPath.map(p => `"${p}"`), `"${targetIndex}"`].join(', ');
    return [
      `// 查找父对象（创建操作）`,
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`,
      `if (DEBUG) console.log('找到父对象:', ${variableName} ? ${variableName}.constructor.name : 'null');`
    ].join('\n');
  }

  /**
   * 生成对象创建代码
   * 使用现有的createGameObject方法，只传入必要属性，不序列化PIXI对象
   */
  private generateObjectCreation(objectType: string, variableName: string, properties: Record<string, any>, objectName?: string): string {
    // 只提取基本的必要属性，避免序列化复杂的PIXI对象
    const params: Record<string, any> = {
      name: objectName || `新${objectType}`
    };

    // 只添加基本的位置和可见性属性
    if (properties.x !== undefined && typeof properties.x === 'number') {
      params.x = properties.x;
    }
    if (properties.y !== undefined && typeof properties.y === 'number') {
      params.y = properties.y;
    }
    if (properties.visible !== undefined && typeof properties.visible === 'boolean') {
      params.visible = properties.visible;
    }

    // 根据对象类型添加特定的简单属性
    switch (objectType) {
      case 'Label':
        if (properties.text && typeof properties.text === 'string') {
          params.text = properties.text;
        }
        break;
      case 'Button':
        if (properties.text && typeof properties.text === 'string') {
          params.text = properties.text;
        }
        break;
      case 'Window':
        if (properties.width && typeof properties.width === 'number') {
          params.width = properties.width;
        }
        if (properties.height && typeof properties.height === 'number') {
          params.height = properties.height;
        }
        break;
    }

    return [
      `// 创建 ${objectType} 对象`,
      `const ${variableName} = createGameObject("${objectType}", ${JSON.stringify(params)});`
    ].join('\n');
  }

  /**
   * 生成类型标记代码
   */
  private generateTypeMarkers(operation: ObjectCreationInfo, variableName: string): string {
    if (!this.config.addTypeMarkers) return '';

    const lines = [
      '// 添加对象标记',
      `${variableName}._isCustomObject = true;`,
      `${variableName}._createdAt = new Date().toISOString();`,
      `${variableName}._objectType = "${operation.objectType}";`
    ];

    // 如果是类型操作，添加类型标记
    if (isTypeOperation(operation)) {
      const typePath = operation.targetObject._rpgEditorPath || [operation.className];
      lines.push(
        `${variableName}._rpgEditorTypeCreated = true;`,
        `${variableName}._rpgEditorParentType = "${operation.className}";`,
        `${variableName}._rpgEditorPath = [${typePath.map((p: string) => `"${p}"`).join(', ')}];`
      );
    }

    return lines.join('\n');
  }

  /**
   * 生成父对象附加代码
   */
  private generateParentAttachment(parentVariableName: string, childVariableName: string): string {
    return [
      '// 添加到父对象',
      `if (${parentVariableName}) {`,
      `    ${parentVariableName}.addChild(${childVariableName});`,
      `    if (DEBUG) console.log('子对象已添加到父对象');`,
      '} else {',
      `    console.warn('父对象未找到，无法添加子对象');`,
      '}'
    ].join('\n');
  }

  /**
   * 生成调试日志
   */
  private generateDebugLog(objectType: string, objectName: string, variableName: string): string {
    return `if (DEBUG) console.log('创建对象完成:', '${objectType}', '${objectName}', ${variableName});`;
  }

  /**
   * 序列化值
   */
  private serializeValue(value: any): string {
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'number' || typeof value === 'boolean') return String(value);
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (Array.isArray(value)) {
      return `[${value.map(v => this.serializeValue(v)).join(', ')}]`;
    }
    if (typeof value === 'object') {
      const pairs = Object.entries(value).map(([k, v]) => `${k}: ${this.serializeValue(v)}`);
      return `{${pairs.join(', ')}}`;
    }
    return JSON.stringify(value);
  }
}

// ==================== 对象删除生成器 ====================

/**
 * 对象删除代码生成器
 */
export class DeletionGenerator {
  private config: ObjectGeneratorConfig;

  constructor(config: ObjectGeneratorConfig) {
    this.config = config;
  }

  /**
   * 生成对象删除代码
   */
  generate(operation: ObjectDeletionInfo, context: any): CodeSnippet {
    const { targetPath, className } = operation;
    const variableName = this.generateVariableName(targetPath, className);
    const parentPath = targetPath.slice(0, -1);
    const parentVariableName = parentPath.length > 0 ?
      this.generateParentVariableName(parentPath) : null;

    const code = [
      this.generateObjectLookup(targetPath, variableName),
      parentVariableName ? this.generateParentLookup(parentPath, parentVariableName) : null,
      this.generateDeletionCode(variableName, parentVariableName),
      this.generateDebugLog(variableName)
    ].filter(Boolean).join('\n\n');

    return {
      code,
      description: `删除 ${className} 对象`,
      dependencies: ['findObjectByScenePath']
    };
  }

  /**
   * 生成变量名
   */
  private generateVariableName(targetPath: string[], className: string): string {
    return `obj_${targetPath.join('_').replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成父对象变量名
   */
  private generateParentVariableName(parentPath: string[]): string {
    return `parent_${parentPath.join('_').replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * 生成对象查找代码
   */
  private generateObjectLookup(targetPath: string[], variableName: string): string {
    const pathArray = targetPath.map(p => `"${p}"`).join(', ');
    return [
      `// 查找要删除的对象`,
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ].join('\n');
  }

  /**
   * 生成父对象查找代码
   */
  private generateParentLookup(parentPath: string[], variableName: string): string {
    const pathArray = parentPath.map(p => `"${p}"`).join(', ');
    return [
      `// 查找父对象`,
      `const ${variableName}Path = [${pathArray}];`,
      `const ${variableName} = findObjectByScenePath(${variableName}Path);`
    ].join('\n');
  }

  /**
   * 生成删除代码
   */
  private generateDeletionCode(variableName: string, parentVariableName: string | null): string {
    const lines = [
      `// 删除对象`,
      `if (${variableName}) {`
    ];

    if (parentVariableName) {
      lines.push(
        `    // 从父对象中移除`,
        `    if (${parentVariableName} && ${parentVariableName}.removeChild) {`,
        `        ${parentVariableName}.removeChild(${variableName});`,
        `    }`
      );
    }

    lines.push(
      `    // 清理对象引用`,
      `    if (${variableName}.destroy) {`,
      `        ${variableName}.destroy();`,
      `    }`,
      `    if (DEBUG) console.log('对象已删除');`,
      `} else {`,
      `    console.warn('要删除的对象未找到');`,
      `}`
    );

    return lines.join('\n');
  }

  /**
   * 生成调试日志
   */
  private generateDebugLog(variableName: string): string {
    return `if (DEBUG) console.log('删除对象:', ${variableName});`;
  }
}
