/**
 * 操作队列管理
 * 用于存储和管理待生成代码的操作
 */

import {
  OperationInfo,
  OperationType,
  OperationMode,
  CodeGenerationError,
  PropertyModificationInfo,
  PropertyChange
} from './types';
import { validateOperationInfo } from './utils';

/**
 * 操作队列管理类
 */
export class OperationQueue {
  // 使用路径作为key，支持同一对象的多种操作类型
  private operations: Map<string, OperationInfo> = new Map();
  private static instance: OperationQueue;
  private isLoading: boolean = false;

  private constructor() { }

  /**
   * 获取单例实例
   */
  public static getInstance(): OperationQueue {
    if (!OperationQueue.instance) {
      OperationQueue.instance = new OperationQueue();
    }
    return OperationQueue.instance;
  }

  /**
   * 生成操作的唯一键
   * 使用对象路径作为键，同一对象只有一个OperationInfo
   */
  private generateOperationKey(operation: OperationInfo): string {
    return operation.objectPath.join('/');
  }

  /**
   * 添加操作到队列
   * 新架构：同一对象路径只有一个OperationInfo，直接修改该OperationInfo
   * @param operation 要添加的操作
   */
  public addOperation(operation: OperationInfo): void {
    // 验证操作
    this.validateOperation(operation);

    // 生成操作键（对象路径）
    const operationKey = this.generateOperationKey(operation);

    // 检查是否已存在该对象的操作
    const existingOperation = this.operations.get(operationKey);

    if (existingOperation) {
      // 如果已存在，根据操作类型处理
      this.updateExistingOperation(existingOperation, operation);
    } else {
      // 如果不存在
      if (operation.operationType === OperationType.DELETE) {
        // 删除操作：如果队列中没有该对象，才添加删除操作
        this.operations.set(operationKey, operation);

      } else {
        // 其他操作：直接添加
        this.operations.set(operationKey, operation);
      }
    }
  }

  /**
   * 更新现有操作
   * @param existingOperation 现有操作
   * @param newOperation 新操作
   */
  private updateExistingOperation(existingOperation: OperationInfo, newOperation: OperationInfo): void {
    if (newOperation.operationType === OperationType.DELETE) {
      // 删除操作：如果队列中已存在该对象，直接从队列删除，不添加删除操作
      const operationKey = this.generateOperationKey(newOperation);
      this.operations.delete(operationKey);

      return;
    } else if (newOperation.operationType === OperationType.MODIFY && existingOperation.operationType === OperationType.MODIFY) {
      // 属性修改：合并属性
      this.mergePropertyModifications(existingOperation as PropertyModificationInfo, newOperation as PropertyModificationInfo);
    } else {
      // 其他情况：替换操作
      const operationKey = this.generateOperationKey(newOperation);
      this.operations.set(operationKey, newOperation);
    }
  }

  /**
   * 合并属性修改
   * @param existingOperation 现有属性修改操作
   * @param newOperation 新属性修改操作
   */
  private mergePropertyModifications(existingOperation: PropertyModificationInfo, newOperation: PropertyModificationInfo): void {
    // 确保现有操作有properties Map
    if (!existingOperation.properties) {
      existingOperation.properties = new Map();

      // 如果现有操作使用旧格式，转换为新格式
      if (existingOperation.propertyName) {
        const propertyChange: PropertyChange = {
          propertyName: existingOperation.propertyName,
          oldValue: existingOperation.oldValue,
          newValue: existingOperation.newValue,
          propertyType: existingOperation.propertyType,
          timestamp: existingOperation.timestamp
        };
        existingOperation.properties.set(existingOperation.propertyName, propertyChange);
      }
    }

    // 添加新操作的属性
    if (newOperation.propertyName) {
      const propertyChange: PropertyChange = {
        propertyName: newOperation.propertyName,
        oldValue: newOperation.oldValue,
        newValue: newOperation.newValue,
        propertyType: newOperation.propertyType,
        timestamp: newOperation.timestamp
      };
      existingOperation.properties.set(newOperation.propertyName, propertyChange);
    }

    // 如果新操作也有properties Map，合并所有属性
    if (newOperation.properties) {
      for (const [propertyName, propertyChange] of newOperation.properties) {
        existingOperation.properties.set(propertyName, propertyChange);
      }
    }

    // 更新时间戳
    existingOperation.timestamp = newOperation.timestamp;
  }

  /**
   * 批量添加操作到队列
   * @param operations 要添加的操作数组
   */
  public addOperations(operations: OperationInfo[]): void {
    this.isLoading = true;
    try {
      operations.forEach(operation => this.addOperation(operation));
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 获取所有操作
   */
  public getOperations(): OperationInfo[] {
    return Array.from(this.operations.values());
  }

  /**
   * 获取指定类型的操作
   * @param type 操作类型
   */
  public getOperationsByType(type: OperationType): OperationInfo[] {
    return this.getOperations().filter(op => op.operationType === type);
  }

  /**
   * 获取指定模式的操作
   * @param mode 操作模式
   */
  public getOperationsByMode(mode: OperationMode): OperationInfo[] {
    return this.getOperations().filter(op => op.operationMode === mode);
  }

  /**
   * 获取指定场景的操作
   * @param sceneName 场景名称
   */
  public getOperationsByScene(sceneName: string): OperationInfo[] {
    return this.getOperations().filter(op =>
      op.objectPath.length > 0 && op.objectPath[0] === sceneName
    );
  }

  /**
   * 获取指定类的操作
   * @param className 类名
   */
  public getOperationsByClass(className: string): OperationInfo[] {
    return this.getOperations().filter(op => op.className === className);
  }

  /**
   * 获取指定路径的操作
   * @param path 对象路径
   */
  public getOperationsByPath(path: string[]): OperationInfo[] {
    const pathKey = path.join('/');
    return this.getOperations().filter(op =>
      op.objectPath.join('/') === pathKey
    );
  }

  /**
   * 清空队列
   */
  public clear(): void {
    this.operations.clear();
    console.log('[OperationQueue] 清空操作队列');
  }

  /**
   * 获取队列大小
   */
  public size(): number {
    return this.operations.size;
  }

  /**
   * 验证操作
   * @param operation 要验证的操作
   */
  private validateOperation(operation: OperationInfo): void {
    // 使用统一的验证逻辑，支持从文件加载的操作
    const errors = validateOperationInfo(operation);
    if (errors.length > 0) {
      throw new CodeGenerationError(errors.join(', '));
    }

    // 基本字段验证
    if (!operation.id) {
      throw new CodeGenerationError('操作缺少ID');
    }
    if (!operation.operationType) {
      throw new CodeGenerationError('操作缺少类型');
    }
    if (!operation.operationMode) {
      throw new CodeGenerationError('操作缺少模式');
    }
    if (!operation.timestamp) {
      throw new CodeGenerationError('操作缺少时间戳');
    }
  }


}