import { recordPropertyModification } from './BackendService';
import { getObjectPath as getPath } from '../../utils/object/objectPro';
import { CodeGenerationService } from '../codeGeneration/CodeGenerationService';

// ==================== 对象生命周期管理相关方法 ====================

/**
 * 对象创建信息接口
 */
export interface ObjectCreationInfo {
  parentPath: string[];      // 父对象路径
  objectType: string;        // 对象类型（如 'Sprite', 'Container', 'Label' 等）
  objectName: string;        // 对象名称
  properties?: Record<string, any>; // 初始属性
  position?: { x: number; y: number }; // 初始位置
}

/**
 * 对象删除信息接口
 */
export interface ObjectDeletionInfo {
  objectPath: string[];      // 对象路径
  objectType: string;        // 对象类型
  objectName: string;        // 对象名称
}

/**
 * 获取对象名称
 */
function getObjectName(object: any): string {
  return object.name || object._rpgEditorName || 'unnamed';
}

/**
 * 提取对象属性
 */
function extractObjectProperties(object: any): Record<string, any> {
  const properties: Record<string, any> = {};

  // 提取基本属性
  if (object.x !== undefined) properties.x = object.x;
  if (object.y !== undefined) properties.y = object.y;
  if (object.width !== undefined) properties.width = object.width;
  if (object.height !== undefined) properties.height = object.height;
  if (object.visible !== undefined) properties.visible = object.visible;
  if (object.alpha !== undefined) properties.alpha = object.alpha;
  if (object.rotation !== undefined) properties.rotation = object.rotation;
  if (object.scale !== undefined) properties.scale = object.scale;
  if (object.anchor !== undefined) properties.anchor = object.anchor;
  if (object.tint !== undefined) properties.tint = object.tint;
  if (object.blendMode !== undefined) properties.blendMode = object.blendMode;
  if (object.filters !== undefined) properties.filters = object.filters;
  if (object.mask !== undefined) properties.mask = object.mask;
  if (object.parent !== undefined) properties.parent = object.parent;
  if (object.children !== undefined) properties.children = object.children;
  if (object.interactive !== undefined) properties.interactive = object.interactive;
  if (object.buttonMode !== undefined) properties.buttonMode = object.buttonMode;
  if (object.cursor !== undefined) properties.cursor = object.cursor;
  if (object.hitArea !== undefined) properties.hitArea = object.hitArea;
  if (object.eventMode !== undefined) properties.eventMode = object.eventMode;
  if (object.sortableChildren !== undefined) properties.sortableChildren = object.sortableChildren;
  if (object.sortDirty !== undefined) properties.sortDirty = object.sortDirty;
  if (object.zIndex !== undefined) properties.zIndex = object.zIndex;
  if (object.zOrder !== undefined) properties.zOrder = object.zOrder;
  if (object.z !== undefined) properties.z = object.z;
  if (object.zIndex !== undefined) properties.zIndex = object.zIndex;
  if (object.zOrder !== undefined) properties.zOrder = object.zOrder;
  if (object.z !== undefined) properties.z = object.z;

  // 提取自定义属性
  for (const key in object) {
    if (key.startsWith('_rpgEditor')) {
      properties[key] = object[key];
    }
  }

  return properties;
}

/**
 * 获取对象位置
 */
function getObjectPosition(object: any): { x: number; y: number } {
  return {
    x: object.x || 0,
    y: object.y || 0
  };
}

/**
 * 记录对象创建操作到后端
 * @param parentObject 父对象
 * @param createdObject 创建的对象
 * @param objectType 对象类型
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export async function recordObjectCreation(
  parentObject: any,
  createdObject: any,
  objectType: string
): Promise<string> {
  try {
    // 获取创建对象的实际路径（对象已经被添加到父对象）
    const actualPath = getPath(createdObject);

    console.log(`记录对象创建: ${objectType} 实际路径:`, actualPath);

    // 使用新的CodeGenerationService记录对象创建
    const codeGenerationService = new CodeGenerationService();

    // 提取基本属性，避免复杂的PIXI对象
    const basicProperties = {
      name: getObjectName(createdObject),
      x: createdObject.x || 0,
      y: createdObject.y || 0,
      visible: createdObject.visible !== undefined ? createdObject.visible : true
    };

    return await codeGenerationService.recordObjectCreationWithActualPath(
      parentObject,
      actualPath,
      objectType,
      basicProperties,
      getObjectName(createdObject)
    );
  } catch (error) {
    console.error('记录对象创建失败:', error);
    throw error;
  }
}

/**
 * 记录对象删除操作到后端
 * @param object 要删除的对象
 * @returns Promise<string> 成功时返回结果，失败时抛出异常
 */
export async function recordObjectDeletion(object: any): Promise<string> {
  try {
    const deletionInfo: ObjectDeletionInfo = {
      objectPath: getPath(object),
      objectType: object.constructor.name,
      objectName: getObjectName(object)
    };

    console.log(`记录对象删除: ${deletionInfo.objectType} "${deletionInfo.objectName}" 从父对象移除`);

    // 使用统一的后端记录方法，使用特殊的属性名来标识这是对象删除操作
    return await recordPropertyModification(
      object,
      `__DELETE_CHILD__${deletionInfo.objectType}`,
      deletionInfo
    );
  } catch (error) {
    console.error('记录对象删除失败:', error);
    throw error;
  }
}

/**
 * 验证对象创建操作
 * @param parentObject 父对象
 * @param objectType 对象类型
 * @returns boolean 是否有效
 */
export function validateObjectCreation(parentObject: any, objectType: string): boolean {
  if (!parentObject) {
    console.error('父对象不能为空');
    return false;
  }

  if (!objectType) {
    console.error('对象类型不能为空');
    return false;
  }

  // 检查父对象是否支持添加子对象
  if (!parentObject.addChild) {
    console.error('父对象不支持添加子对象');
    return false;
  }

  return true;
}

/**
 * 验证对象删除操作
 * @param object 要删除的对象
 * @returns boolean 是否有效
 */
export function validateObjectDeletion(object: any): boolean {
  if (!object) {
    console.error('要删除的对象不能为空');
    return false;
  }

  // 检查对象是否有父对象
  if (!object.parent) {
    console.error('对象没有父对象，无法删除');
    return false;
  }

  return true;
}

/**
 * 获取对象类型的默认属性
 * @param objectType 对象类型
 * @returns Record<string, any> 默认属性
 */
export function getDefaultPropertiesForType(objectType: string): Record<string, any> {
  const defaultProperties: Record<string, any> = {
    x: 0,
    y: 0,
    visible: true,
    alpha: 1,
    rotation: 0,
    scale: { x: 1, y: 1 },
    anchor: { x: 0.5, y: 0.5 },
    tint: 0xFFFFFF,
    blendMode: 0,
    filters: [],
    mask: null,
    interactive: false,
    buttonMode: false,
    cursor: 'pointer',
    hitArea: null,
    eventMode: 'none',
    sortableChildren: false,
    sortDirty: false,
    zIndex: 0,
    zOrder: 0,
    z: 0
  };

  // 根据对象类型添加特定属性
  switch (objectType) {
    case 'Sprite':
      defaultProperties.texture = null;
      break;
    case 'Container':
      // Container 没有额外的默认属性
      break;
    case 'Graphics':
      defaultProperties.lineStyle = { width: 1, color: 0x000000, alpha: 1 };
      defaultProperties.fillStyle = { color: 0xFFFFFF, alpha: 1 };
      break;
    case 'Text':
      defaultProperties.text = '';
      defaultProperties.style = {
        fontFamily: 'Arial',
        fontSize: 24,
        fontStyle: 'normal',
        fontWeight: 'normal',
        fill: 0x000000,
        align: 'left',
        wordWrap: false,
        wordWrapWidth: 0,
        lineHeight: 0,
        letterSpacing: 0,
        textBaseline: 'alphabetic',
        dropShadow: false,
        dropShadowColor: 0x000000,
        dropShadowBlur: 0,
        dropShadowAngle: 0,
        dropShadowDistance: 0,
        padding: 0,
        textShadow: false,
        textShadowColor: 0x000000,
        textShadowBlur: 0,
        textShadowAngle: 0,
        textShadowDistance: 0
      };
      break;
    case 'BitmapText':
      defaultProperties.text = '';
      defaultProperties.fontName = 'Arial';
      defaultProperties.fontSize = 24;
      defaultProperties.align = 'left';
      defaultProperties.tint = 0xFFFFFF;
      break;
    case 'Label':
      defaultProperties.text = '';
      defaultProperties.style = {
        fontFamily: 'Arial',
        fontSize: 24,
        fontStyle: 'normal',
        fontWeight: 'normal',
        fill: 0x000000,
        align: 'left',
        wordWrap: false,
        wordWrapWidth: 0,
        lineHeight: 0,
        letterSpacing: 0,
        textBaseline: 'alphabetic',
        dropShadow: false,
        dropShadowColor: 0x000000,
        dropShadowBlur: 0,
        dropShadowAngle: 0,
        dropShadowDistance: 0,
        padding: 0,
        textShadow: false,
        textShadowColor: 0x000000,
        textShadowBlur: 0,
        textShadowAngle: 0,
        textShadowDistance: 0
      };
      break;
  }

  return defaultProperties;
}
